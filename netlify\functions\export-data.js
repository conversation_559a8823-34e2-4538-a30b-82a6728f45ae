// Netlify Function to export all data as downloadable files
const fs = require('fs').promises;
const path = require('path');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const dataDir = '/tmp/shein-data';
    const queryParams = event.queryStringParameters || {};
    const exportType = queryParams.type || 'winners'; // 'winners', 'clients', 'all'
    const format = queryParams.format || 'txt'; // 'txt', 'csv', 'json'

    let exportData = '';
    let filename = 'shein-export';
    let contentType = 'text/plain';

    if (exportType === 'winners' || exportType === 'all') {
      const winnersFilePath = path.join(dataDir, 'winners.json');
      let winners = [];
      
      try {
        const winnersContent = await fs.readFile(winnersFilePath, 'utf8');
        winners = JSON.parse(winnersContent);
      } catch (err) {
        // File doesn't exist
      }

      if (format === 'csv') {
        exportData += 'Timestamp,Client Name,Prize,Client Info\n';
        winners.forEach(winner => {
          const clientInfo = JSON.stringify(winner.clientInfo || {}).replace(/"/g, '""');
          exportData += `"${winner.timestamp}","${winner.clientName}","${winner.prize}","${clientInfo}"\n`;
        });
        contentType = 'text/csv';
        filename = 'shein-winners.csv';
      } else if (format === 'json') {
        exportData = JSON.stringify(winners, null, 2);
        contentType = 'application/json';
        filename = 'shein-winners.json';
      } else {
        // Default to txt
        exportData = winners
          .map(w => `${w.timestamp} - ${w.clientName}: ${w.prize}`)
          .join('\n');
        filename = 'shein-winners.txt';
      }
    }

    if (exportType === 'clients' || exportType === 'all') {
      const clientsFilePath = path.join(dataDir, 'clients.json');
      let clients = [];
      
      try {
        const clientsContent = await fs.readFile(clientsFilePath, 'utf8');
        clients = JSON.parse(clientsContent);
      } catch (err) {
        // File doesn't exist
      }

      if (exportType === 'all') {
        exportData += '\n\n=== CLIENT INFORMATION ===\n\n';
      }

      if (format === 'csv' && exportType === 'clients') {
        exportData = 'Name,Total Wins,First Win Prize,First Win Date,Last Win Prize,Last Win Date\n';
        clients.forEach(client => {
          exportData += `"${client.name}","${client.totalWins || 0}","${client.firstWin?.prize || ''}","${client.firstWin?.timestamp || ''}","${client.lastWin?.prize || ''}","${client.lastWin?.timestamp || ''}"\n`;
        });
        contentType = 'text/csv';
        filename = 'shein-clients.csv';
      } else if (format === 'json' && exportType === 'clients') {
        exportData = JSON.stringify(clients, null, 2);
        contentType = 'application/json';
        filename = 'shein-clients.json';
      } else {
        // Default to txt
        const clientsText = clients
          .map(c => `${c.name} - Total Wins: ${c.totalWins || 0} - Last Win: ${c.lastWin?.prize || 'None'} (${c.lastWin?.timestamp || 'N/A'})`)
          .join('\n');
        
        if (exportType === 'clients') {
          exportData = clientsText;
          filename = 'shein-clients.txt';
        } else {
          exportData += clientsText;
          filename = 'shein-complete-export.txt';
        }
      }
    }

    if (exportType === 'all' && format === 'json') {
      // Combine all data for JSON export
      const winnersFilePath = path.join(dataDir, 'winners.json');
      const clientsFilePath = path.join(dataDir, 'clients.json');
      
      let winners = [];
      let clients = [];
      
      try {
        const winnersContent = await fs.readFile(winnersFilePath, 'utf8');
        winners = JSON.parse(winnersContent);
      } catch (err) {}
      
      try {
        const clientsContent = await fs.readFile(clientsFilePath, 'utf8');
        clients = JSON.parse(clientsContent);
      } catch (err) {}

      exportData = JSON.stringify({
        exportDate: new Date().toISOString(),
        totalWinners: winners.length,
        totalClients: clients.length,
        winners: winners,
        clients: clients
      }, null, 2);
      
      contentType = 'application/json';
      filename = 'shein-complete-export.json';
    }

    return {
      statusCode: 200,
      headers: {
        ...headers,
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`
      },
      body: exportData || 'No data available'
    };

  } catch (error) {
    console.error('Error exporting data:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Failed to export data',
        details: error.message
      })
    };
  }
};
