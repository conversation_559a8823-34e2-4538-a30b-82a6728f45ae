<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shein Fortune Wheel</title>
    <link rel="icon" type="image/png" href="shein-logo.png">
    <link rel="shortcut icon" type="image/png" href="shein-logo.png">
    <link rel="stylesheet" href="wheel-styles.css">
    <!-- SheetJS for XLSX export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Database Client -->
    <script src="database-client.js"></script>
</head>
<body>
    <div class="container">
        <!-- Client Info Bar -->
        <div class="client-info-bar">
            <div class="client-welcome">
                <span class="welcome-text">Welcome, <strong id="clientNameDisplay"></strong>!</span>
            </div>
            <div class="client-actions">
                <button id="backToRegister" class="btn-secondary">Change Name</button>
                <button id="toggleAdmin" class="btn-admin" style="display: none;">Admin Panel</button>
            </div>
        </div>

        <div class="main-content">
            <!-- Wheel Section (Hidden in Admin Mode) -->
            <div id="wheelSection" class="wheel-section">
                <div class="wheel-container">
                    <canvas id="wheelCanvas" width="600" height="600"></canvas>
                    <div class="center-logo">
                        <img id="centerImage" src="shein-logo.png" alt="Shein Logo">
                    </div>
                </div>
                <button id="spinBtn" class="spin-button">SPIN THE WHEEL</button>
                <div class="result-display" id="resultDisplay">Ready to spin! Good luck! 🍀</div>
            </div>

            <!-- Professional Dashboard (Hidden by default) -->
            <div class="admin-panel" id="adminPanel" style="display: none;">
                <div class="dashboard-header">
                    <h2>📊 Shein Fortune Wheel - Professional Dashboard</h2>
                    <div class="dashboard-header-actions">
                        <div id="connectionStatus" class="connection-status">🔄 Checking connection...</div>
                        <button id="closeAdmin" class="btn-secondary">Close Dashboard</button>
                    </div>
                </div>

                <!-- Dashboard Overview Cards -->
                <div class="dashboard-overview">
                    <div class="overview-cards">
                        <div class="overview-card">
                            <div class="card-icon">👥</div>
                            <div class="card-content">
                                <h3 id="totalClientsCount">0</h3>
                                <p>Total Clients</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">🎯</div>
                            <div class="card-content">
                                <h3 id="totalWinnersCount">0</h3>
                                <p>Winners</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">💰</div>
                            <div class="card-content">
                                <h3 id="totalRevenueCount">€0</h3>
                                <p>Total Revenue</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">📈</div>
                            <div class="card-content">
                                <h3 id="winRateCount">0%</h3>
                                <p>Win Rate</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Client Management Section -->
                <div class="client-management-section">
                    <div class="section-header">
                        <h3>👥 Client Management</h3>
                        <div class="section-actions">
                            <button id="addClientBtn" class="btn-primary">➕ Add Client</button>
                            <button id="exportJsonBtn" class="btn-secondary">📄 Export JSON</button>
                            <button id="exportExcelBtn" class="btn-secondary">📊 Export Excel</button>
                            <button id="importDataBtn" class="btn-secondary">📥 Import Data</button>
                        </div>
                    </div>

                    <!-- Add Client Form (Hidden by default) -->
                    <div id="addClientForm" class="add-client-form" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="newClientName">Client Name:</label>
                                <input type="text" id="newClientName" placeholder="Enter client name" required>
                            </div>
                            <div class="form-group">
                                <label for="newClientPayment">Spending Amount (€):</label>
                                <input type="number" id="newClientPayment" placeholder="30-200" min="30" max="200" step="0.01" required>
                            </div>
                            <div class="form-actions">
                                <button id="saveClient" class="btn-primary">💾 Save Client</button>
                                <button id="cancelAddClient" class="btn-secondary">❌ Cancel</button>
                            </div>
                        </div>
                    </div>

                    <!-- Clients Table -->
                    <div class="clients-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <input type="text" id="clientSearch" placeholder="🔍 Search clients..." />
                            </div>
                            <div class="table-info">
                                <span id="tableInfo">Showing 0 clients</span>
                            </div>
                        </div>
                        <div class="table-wrapper">
                            <table id="clientsTable" class="clients-table">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAll"></th>
                                        <th>Client Name</th>
                                        <th>Payment Amount</th>
                                        <th>Tier</th>
                                        <th>Attempts</th>
                                        <th>Status</th>
                                        <th>Last Prize</th>
                                        <th>Win Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="clientsTableBody">
                                    <!-- Dynamic content -->
                                </tbody>
                            </table>
                        </div>
                        <div class="table-actions">
                            <button id="deleteSelectedBtn" class="btn-danger">🗑️ Delete Selected</button>
                            <button id="refreshTableBtn" class="btn-secondary">🔄 Refresh</button>
                        </div>
                    </div>
                </div>

                <!-- Prize Management Section -->
                <div class="prize-management-section">
                    <div class="section-header">
                        <h3>🎁 Prize & Winning Chances Management</h3>
                    </div>

                    <div class="prize-tiers">
                        <!-- Tier 1 Configuration -->
                        <div class="tier-config">
                            <div class="tier-header">
                                <h4>🥉 Tier 1 (30-99€) - <span id="tier1SegmentCount">5</span> Segments</h4>
                                <div class="tier-controls">
                                    <button type="button" class="btn-small btn-primary" onclick="addSegment('tier1')">➕ Add Segment</button>
                                    <button type="button" class="btn-small btn-secondary" onclick="removeSegment('tier1')">➖ Remove Segment</button>
                                </div>
                            </div>
                            <div class="tier-prizes" id="tier1Prizes">
                                <!-- Dynamic segments will be added here -->
                            </div>
                            <div class="tier-total">
                                Total: <span id="tier1Total">100</span>%
                            </div>
                        </div>

                        <!-- Tier 2 Configuration -->
                        <div class="tier-config">
                            <div class="tier-header">
                                <h4>🥇 Tier 2 (100-200€) - <span id="tier2SegmentCount">7</span> Segments</h4>
                                <div class="tier-controls">
                                    <button type="button" class="btn-small btn-primary" onclick="addSegment('tier2')">➕ Add Segment</button>
                                    <button type="button" class="btn-small btn-secondary" onclick="removeSegment('tier2')">➖ Remove Segment</button>
                                </div>
                            </div>
                            <div class="tier-prizes" id="tier2Prizes">
                                <!-- Dynamic segments will be added here -->
                            </div>
                            <div class="tier-total">
                                Total: <span id="tier2Total">100</span>%
                            </div>
                        </div>
                    </div>

                    <div class="prize-actions">
                        <button id="savePrizeConfig" class="btn-primary">💾 Save Configuration</button>
                        <button id="resetPrizeConfig" class="btn-secondary">🔄 Reset to Default</button>
                    </div>
                </div>

                <!-- Hidden File Inputs -->
                <input type="file" id="dataFileInput" accept=".json" style="display: none;">
                <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
            </div>
        </div>
    </div>

    <!-- Winner Popup Modal -->
    <div id="winnerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎉 CONGRATULATIONS! 🎉</h2>
            </div>
            <div class="modal-body">
                <div class="winner-info">
                    <div class="winner-name" id="modalWinnerName"></div>
                    <div class="winner-prize" id="modalWinnerPrize"></div>
                    <div class="celebration-text">You've won an amazing prize!</div>
                </div>
                <div class="confetti-container">
                    <div class="confetti"></div>
                    <div class="confetti"></div>
                    <div class="confetti"></div>
                    <div class="confetti"></div>
                    <div class="confetti"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="closeModal" class="btn-primary modal-btn">Awesome!</button>
            </div>
        </div>
    </div>

    <script src="wheel-script.js"></script>
</body>
</html>
