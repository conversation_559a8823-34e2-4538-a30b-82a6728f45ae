/* <PERSON>in <PERSON> Wheel Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Client Info Bar */
.client-info-bar {
    background: white;
    border-radius: 15px;
    padding: 15px 25px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.welcome-text {
    font-size: 1.2rem;
    color: #333;
}

.client-actions {
    display: flex;
    gap: 10px;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: start;
}

/* Wheel Section */
.wheel-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.wheel-container {
    position: relative;
    margin-bottom: 40px;
}

#wheelCanvas {
    border-radius: 50%;
    box-shadow: 0 15px 50px rgba(0,0,0,0.3);
    transition: transform 0.1s ease;
    width: 500px;
    height: 500px;
    border: 8px solid white;
}

.center-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    z-index: 10;
    border: 4px solid #f0f0f0;
}

#centerImage {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}



.spin-button {
    background: linear-gradient(45deg, #FF6BB7, #FF8CC8);
    color: white;
    border: none;
    padding: 20px 50px;
    font-size: 1.4rem;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 183, 0.4);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 30px;
}

.spin-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 183, 0.6);
}

.spin-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.result-display {
    padding: 20px 30px;
    background: #f8f9fa;
    border-radius: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 1.2rem;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

/* Professional Dashboard */
.admin-panel {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #4ECDC4, #45B7B8);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.dashboard-header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Dashboard Overview Cards */
.dashboard-overview {
    padding: 30px;
    background: #f8f9fa;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4ECDC4, #45B7B8);
    border-radius: 50%;
    color: white;
}

.card-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.card-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}
/* Client Management Section */
.client-management-section {
    padding: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.section-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Add Client Form */
.add-client-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-actions {
    display: flex;
    gap: 10px;
}

/* Professional Table Styles */
.clients-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.search-box input {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    width: 300px;
    font-size: 0.9rem;
}

.table-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.table-wrapper {
    overflow-x: auto;
}

.clients-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.clients-table th {
    background: #2c3e50;
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: 500;
    white-space: nowrap;
}

.clients-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.clients-table tbody tr:hover {
    background: #f8f9fa;
}

.clients-table tbody tr:nth-child(even) {
    background: #fdfdfd;
}

.clients-table tbody tr:nth-child(even):hover {
    background: #f0f0f0;
}

/* Status badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.status-winner {
    background: #d4edda;
    color: #155724;
}

.status-active {
    background: #d1ecf1;
    color: #0c5460;
}

.status-exhausted {
    background: #f8d7da;
    color: #721c24;
}

/* Tier badges */
.tier-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
    display: inline-block;
}

.tier-1 {
    background: #fff3cd;
    color: #856404;
}

.tier-2 {
    background: #d4edda;
    color: #155724;
}

.table-actions {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    border-top: 1px solid #e9ecef;
}

/* Action buttons in table */
.action-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background: #007bff;
    color: white;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}
/* Prize Management Section */
.prize-management-section {
    padding: 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.prize-tiers {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.tier-config {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.tier-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.tier-controls {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-small.btn-primary {
    background: #4ECDC4;
    color: white;
}

.btn-small.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-small:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.tier-prizes {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.prize-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: 10px;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;
}

.prize-item.removable {
    grid-template-columns: auto 1fr auto auto auto;
}

.remove-segment-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-segment-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.tier-total {
    margin-top: 15px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
}

.tier-total.invalid {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.tier-total.valid {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.prize-item label {
    font-weight: 500;
    color: #2c3e50;
    min-width: 60px;
}

.prize-item input[type="text"] {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.prize-item input[type="number"] {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 60px;
    text-align: center;
    font-size: 0.9rem;
}

.prize-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.excel-management, .tier-management, .wheel-controls, .winners-history {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.prize-management h3, .wheel-controls h3, .winners-history h3 {
    color: #FF6BB7;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* Collapsible Sections */
.collapsible-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.collapsible-header:hover {
    color: #FF8CC8;
}

.toggle-icon {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.toggle-icon.rotated {
    transform: rotate(-90deg);
}

.collapsible-content {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible-content.collapsed {
    max-height: 0;
}

/* Form Elements */
input[type="text"], input[type="number"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 10px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: #FF6BB7;
}

input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

input[type="range"] {
    width: 100%;
    margin: 10px 0;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-danger, .btn-admin {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
}

.btn-primary {
    background: #FF6BB7;
    color: white;
}

.btn-secondary {
    background: #4ECDC4;
    color: white;
}

.btn-danger {
    background: #ff4757;
    color: white;
}

.btn-admin {
    background: #6c5ce7;
    color: white;
}

.btn-primary:hover, .btn-secondary:hover, .btn-danger:hover, .btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Prize Management */
.add-prize-form {
    display: grid;
    grid-template-columns: 1fr 80px 60px 100px;
    gap: 10px;
    align-items: end;
    margin-bottom: 20px;
}

.prize-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #FF6BB7;
}

.prize-color {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin-right: 12px;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.prize-info {
    flex: 1;
    font-size: 1rem;
}

.prize-actions button {
    padding: 6px 12px;
    font-size: 0.8rem;
    margin-left: 5px;
}

/* History */
.history-item {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #FF6BB7;
}

.history-item .timestamp {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

/* Export and Data Management */
.export-buttons, .data-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.export-buttons button, .data-actions button {
    flex: 1;
    min-width: 120px;
    font-size: 0.9rem;
    padding: 8px 12px;
}

.data-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border: 2px solid #e0e0e0;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-item {
    font-size: 0.9rem;
    color: #333;
}

.stat-item strong {
    color: #FF6BB7;
}

/* Excel Management */
.excel-actions, .client-management {
    margin-bottom: 20px;
}

.excel-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.add-client-form {
    display: grid;
    grid-template-columns: 1fr 120px 100px;
    gap: 10px;
    align-items: end;
    margin-top: 10px;
}

.excel-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    border: 2px solid #e0e0e0;
}

.excel-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.client-not-found, .error-message, .no-attempts, .final-attempt {
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
}

.client-not-found {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffeaa7;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #f5c6cb;
}

.no-attempts {
    background: #d1ecf1;
    color: #0c5460;
    border: 2px solid #bee5eb;
}

.final-attempt {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffeaa7;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Tier Management */
.tier-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.tier-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border: 2px solid #e0e0e0;
}

.tier-section h4 {
    color: #FF6BB7;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.tier-prizes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tier-prize-item {
    background: white;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #ddd;
}

.tier-prize-item .prize-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.prize-name {
    flex: 1;
    font-weight: bold;
    color: #333;
}

.chance-input {
    width: 60px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

.chance-input:focus {
    outline: none;
    border-color: #FF6BB7;
    box-shadow: 0 0 0 2px rgba(255, 107, 183, 0.2);
}

/* Spinning Animation */
.spinning {
    animation: spin linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Winner Modal - Same as before but enhanced */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
    margin: 8% auto;
    padding: 0;
    border-radius: 25px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.5s ease;
    overflow: hidden;
}

.modal-header {
    text-align: center;
    padding: 40px 30px 30px;
    color: white;
}

.modal-header h2 {
    font-size: 3rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: bounce 1s ease infinite alternate;
}

.modal-body {
    padding: 30px;
    text-align: center;
    position: relative;
    background: white;
    margin: 0 30px;
    border-radius: 20px;
    box-shadow: inset 0 4px 15px rgba(0, 0, 0, 0.1);
}

.winner-name {
    font-size: 2.2rem;
    font-weight: bold;
    color: #FF6BB7;
    margin-bottom: 20px;
}

.winner-prize {
    font-size: 2.8rem;
    font-weight: bold;
    color: #4ECDC4;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.celebration-text {
    font-size: 1.4rem;
    color: #666;
    font-style: italic;
}

.modal-footer {
    padding: 30px;
    text-align: center;
}

.modal-btn {
    font-size: 1.4rem;
    padding: 18px 50px;
    border-radius: 50px;
    background: linear-gradient(45deg, #FF6BB7, #FF8CC8);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(255, 107, 183, 0.4);
}

.modal-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 183, 0.6);
}

/* Confetti Animation */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.confetti {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #FF6BB7;
    animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #FF6BB7; }
.confetti:nth-child(2) { left: 30%; animation-delay: 0.5s; background: #4ECDC4; }
.confetti:nth-child(3) { left: 50%; animation-delay: 1s; background: #FFB6C1; }
.confetti:nth-child(4) { left: 70%; animation-delay: 1.5s; background: #87CEEB; }
.confetti:nth-child(5) { left: 90%; animation-delay: 2s; background: #DDA0DD; }

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    from { transform: translateY(0px); }
    to { transform: translateY(-10px); }
}

@keyframes confetti-fall {
    0% { transform: translateY(-100px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(500px) rotate(720deg); opacity: 0; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .admin-panel {
        width: 100%;
    }
}

@media (max-width: 768px) {
    #wheelCanvas {
        width: 350px;
        height: 350px;
    }

    .center-logo {
        width: 70px;
        height: 70px;
    }

    #centerImage {
        width: 50px;
        height: 50px;
    }



    .add-prize-form {
        grid-template-columns: 1fr;
    }

    .client-info-bar {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* Enhanced Client Management Styles */
.excel-management h3 {
    color: #FF6BB7;
    margin-bottom: 20px;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.excel-actions h4, .client-management h4 {
    color: #4ECDC4;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.file-help {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    border-left: 4px solid #4ECDC4;
    margin-top: 10px;
}

.file-help small {
    color: #666;
    font-size: 0.9rem;
}

.add-client-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #FF6BB7;
    box-shadow: 0 0 0 3px rgba(255, 107, 183, 0.1);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.85rem;
}

/* Enhanced Client Info Display */
.client-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #dee2e6;
}

.client-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.spending-info, .tier-info, .attempts-info, .last-prize-info, .last-date-info {
    background: white;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #FF6BB7;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tier-info {
    border-left-color: #4ECDC4;
}

.attempts-info {
    border-left-color: #FFB6C1;
}

.last-prize-info {
    border-left-color: #87CEEB;
}

.last-date-info {
    border-left-color: #DDA0DD;
}

.ready-message {
    background: linear-gradient(135deg, #4ECDC4, #45B7B8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.winner-message {
    background: linear-gradient(135deg, #FF6BB7, #FF8CC8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    font-size: 1.1rem;
    animation: pulse 2s infinite;
}

.no-attempts-message {
    background: linear-gradient(135deg, #FFA500, #FF6347);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
}

.already-won {
    background: linear-gradient(135deg, #98FB98, #90EE90);
    color: #2d5a2d;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    font-size: 1.2rem;
    border: 3px solid #228B22;
}

.client-not-registered {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #dc3545;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.client-not-registered .error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.client-not-registered h3 {
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    font-weight: bold;
}

.client-not-registered p {
    margin: 10px 0;
    font-size: 1rem;
    line-height: 1.4;
}

.client-not-registered .contact-info {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
}

.client-not-registered .contact-info p {
    margin: 0;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Error Handling Styles */
.error-notification, .success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    display: none;
    animation: slideIn 0.3s ease-out;
}

.error-notification {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-left: 5px solid #dc3545;
}

.success-notification {
    background: linear-gradient(135deg, #51cf66, #40c057);
    border-left: 5px solid #28a745;
}

.error-content, .success-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.error-icon, .success-icon {
    font-size: 1.5rem;
    margin-right: 12px;
}

.error-message, .success-message {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.error-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.critical-error {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #dc3545;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
    margin: 20px;
}

.critical-error .error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
}

.critical-error h3 {
    margin: 0 0 15px 0;
    font-size: 1.8rem;
    font-weight: bold;
}

.critical-error p {
    margin: 10px 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
    flex-wrap: wrap;
}

.error-actions .btn-primary,
.error-actions .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.error-actions .btn-primary {
    background: linear-gradient(135deg, #4ECDC4, #45B7B8);
    color: white;
}

.error-actions .btn-secondary {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.error-actions .btn-primary:hover,
.error-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Database Connection Status */
.connection-status {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-top: 10px;
    text-align: center;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
}

.connection-status.offline {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.sync-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    display: none;
    animation: slideDown 0.3s ease-out;
}

.sync-content {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    background: linear-gradient(135deg, #4ECDC4, #45B7B8);
    color: white;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    font-weight: 500;
}

.sync-icon {
    font-size: 1.2rem;
    margin-right: 10px;
    animation: spin 1s linear infinite;
}

@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Data Sharing Styles */
.data-sharing-section {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.data-sharing-section h4 {
    color: #4ECDC4;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sharing-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.sharing-help {
    background: white;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #4ECDC4;
}

.sharing-help small {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.connection-status.checking {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Client Management Table */
.client-table-section {
    margin-top: 20px;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
}

.table-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
    align-items: center;
}

.search-input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.search-input:focus {
    outline: none;
    border-color: #FF6BB7;
}

.table-container {
    overflow-x: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.client-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.client-table th,
.client-table td {
    padding: 10px 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.client-table th {
    background: linear-gradient(135deg, #FF6BB7, #FF8CC8);
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.client-table tbody tr:hover {
    background-color: #f8f9fa;
}

.client-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.client-name {
    font-weight: bold;
    color: #333;
}

.client-spending {
    font-weight: bold;
    color: #4ECDC4;
}

.client-tier {
    font-weight: bold;
}

.client-attempts {
    font-family: monospace;
}

.client-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-winner {
    background: #fff3cd;
    color: #856404;
}

.status-completed {
    background: #f8d7da;
    color: #721c24;
}

.client-actions {
    display: flex;
    gap: 5px;
}

.btn-edit, .btn-delete, .btn-reset {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #17a2b8;
    color: white;
}

.btn-edit:hover {
    background: #138496;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.btn-reset {
    background: #ffc107;
    color: #212529;
}

.btn-reset:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
}

.btn-danger:hover {
    background: #c82333;
}

.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-top: 1px solid #ddd;
}

.table-pagination button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.table-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.table-pagination button:not(:disabled):hover {
    background: #e9ecef;
}

#pageInfo {
    font-weight: bold;
    color: #666;
}

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 15px;
    }

    .main-content {
        gap: 20px;
    }

    .admin-panel {
        width: 350px;
    }

    .wheel-container canvas {
        width: 400px;
        height: 400px;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .client-info-bar {
        flex-direction: column;
        gap: 10px;
        text-align: center;
        padding: 12px 15px;
    }

    .welcome-text {
        font-size: 1rem;
    }

    .client-actions {
        justify-content: center;
    }

    .wheel-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .wheel-container canvas {
        width: 300px;
        height: 300px;
    }

    .center-logo {
        width: 60px;
        height: 60px;
    }

    .center-logo img {
        width: 50px;
        height: 50px;
    }



    .spin-button {
        padding: 12px 24px;
        font-size: 1rem;
        margin-top: 15px;
    }

    .result-display {
        padding: 15px;
        font-size: 0.9rem;
        margin-top: 15px;
    }

    .admin-panel {
        width: 100%;
        max-width: none;
    }

    .excel-management, .tier-management, .wheel-controls, .winners-history {
        padding: 15px;
    }

    .excel-management h3, .tier-management h3, .wheel-controls h3, .winners-history h3 {
        font-size: 1.1rem;
    }

    .client-details {
        grid-template-columns: 1fr;
    }

    .file-buttons {
        flex-direction: column;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        min-width: auto;
    }

    .client-table {
        font-size: 0.8rem;
    }

    .client-table th,
    .client-table td {
        padding: 6px 4px;
    }

    .client-actions {
        flex-direction: column;
        gap: 2px;
    }

    .btn-edit, .btn-delete, .btn-reset {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 8px;
    }

    .client-info-bar {
        padding: 10px;
    }

    .welcome-text {
        font-size: 0.9rem;
    }

    .wheel-container canvas {
        width: 250px;
        height: 250px;
    }

    .center-logo {
        width: 50px;
        height: 50px;
    }

    .center-logo img {
        width: 40px;
        height: 40px;
    }



    .spin-button {
        padding: 10px 20px;
        font-size: 0.9rem;
        min-height: 45px;
    }

    .result-display {
        padding: 12px;
        font-size: 0.8rem;
    }

    .spending-info, .tier-info, .attempts-info, .last-prize-info, .last-date-info {
        padding: 8px;
        font-size: 0.85rem;
    }

    .ready-message, .winner-message, .no-attempts-message {
        padding: 10px;
        font-size: 0.9rem;
    }

    .excel-management, .tier-management, .wheel-controls, .winners-history {
        padding: 12px;
    }

    .form-group input {
        padding: 8px;
        font-size: 0.9rem;
    }

    .btn-primary, .btn-secondary {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .file-buttons {
        gap: 8px;
    }

    .add-client-form {
        padding: 15px;
    }

    .client-table {
        font-size: 0.7rem;
    }

    .client-table th,
    .client-table td {
        padding: 4px 2px;
    }
}
