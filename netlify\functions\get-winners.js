// Netlify Function to retrieve winner data
const fs = require('fs').promises;
const path = require('path');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // <PERSON>le preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const dataDir = '/tmp/shein-data';
    const jsonFilePath = path.join(dataDir, 'winners.json');
    const clientsFilePath = path.join(dataDir, 'clients.json');

    let winners = [];
    let clients = [];

    // Read winners data
    try {
      const winnersContent = await fs.readFile(jsonFilePath, 'utf8');
      winners = JSON.parse(winnersContent);
    } catch (err) {
      // File doesn't exist yet
    }

    // Read clients data
    try {
      const clientsContent = await fs.readFile(clientsFilePath, 'utf8');
      clients = JSON.parse(clientsContent);
    } catch (err) {
      // File doesn't exist yet
    }

    // Get query parameters
    const queryParams = event.queryStringParameters || {};
    const format = queryParams.format || 'json';
    const limit = parseInt(queryParams.limit) || 100;

    // Limit results
    const limitedWinners = winners.slice(-limit);

    if (format === 'txt') {
      // Return as plain text
      const textData = limitedWinners
        .map(w => `${w.timestamp} - ${w.clientName}: ${w.prize}`)
        .join('\n');

      return {
        statusCode: 200,
        headers: {
          ...headers,
          'Content-Type': 'text/plain',
          'Content-Disposition': 'attachment; filename="shein-winners.txt"'
        },
        body: textData
      };
    }

    // Return JSON data
    return {
      statusCode: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        winners: limitedWinners,
        clients: clients,
        totalWinners: winners.length,
        totalClients: clients.length
      })
    };

  } catch (error) {
    console.error('Error retrieving data:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Failed to retrieve data',
        details: error.message
      })
    };
  }
};
