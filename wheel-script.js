// Shein Fortune Wheel - Main Page JavaScript
class FortuneWheel {
    constructor() {
        try {
            this.canvas = document.getElementById('wheelCanvas');
            if (!this.canvas) {
                throw new Error('Wheel canvas not found');
            }

            this.ctx = this.canvas.getContext('2d');
            if (!this.ctx) {
                throw new Error('Canvas context not available');
            }

            this.centerX = this.canvas.width / 2;
            this.centerY = this.canvas.height / 2;
            this.radius = 280; // Bigger wheel
            this.currentRotation = 0;
            this.isSpinning = false;
            this.currentClient = '';
            this.isAdminMode = false;
            this.clientData = null;
            this.clientAttempts = 0;

            // Initialize error handling
            this.setupErrorHandling();

            // Initialize 404 handling
            this.setup404Handling();
        } catch (error) {
            this.handleCriticalError('Failed to initialize Fortune Wheel', error);
        }

        // Payment-based wheel configurations
        this.wheelConfigs = {
            'tier1': { // 30-99 euros
                prizes: [
                    { name: 'Surprise', chance: 25, color: '#4ECDC4' },
                    { name: '10% Reduction', chance: 25, color: '#FFB6C1' },
                    { name: 'Try Again!', chance: 25, color: '#FFA500' },
                    { name: 'Better Luck Next Time', chance: 25, color: '#FF6347' },
                    { name: '5€ Voucher', chance: 0, color: '#FF6BB7' } // Hidden prize, will be shown but with 0% chance
                ]
            },
            'tier2': { // 100-200 euros
                prizes: [
                    { name: 'Surprise', chance: 16.67, color: '#4ECDC4' },
                    { name: '10% Reduction', chance: 16.67, color: '#FFB6C1' },
                    { name: '20% Reduction', chance: 16.67, color: '#DDA0DD' },
                    { name: 'Try Again!', chance: 16.67, color: '#FFA500' },
                    { name: 'Better Luck Next Time', chance: 16.67, color: '#FF6347' },
                    { name: '5€ Voucher', chance: 8.33, color: '#FF6BB7' },
                    { name: '10€ Voucher', chance: 8.33, color: '#87CEEB' }
                ]
            },
            'admin': { // Admin default
                prizes: [
                    { name: '10€ Gift Card', chance: 25, color: '#FF6BB7' },
                    { name: '20€ Gift Card', chance: 20, color: '#4ECDC4' },
                    { name: 'Free Shipping', chance: 25, color: '#FFB6C1' },
                    { name: '15€ Gift Card', chance: 15, color: '#87CEEB' },
                    { name: '25€ Gift Card', chance: 10, color: '#DDA0DD' },
                    { name: '50€ Gift Card', chance: 5, color: '#98FB98' }
                ]
            }
        };

        this.prizes = this.wheelConfigs.admin.prizes;
        
        this.winners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');
        
        // Load saved prizes from localStorage
        const savedPrizes = localStorage.getItem('sheinPrizes');
        if (savedPrizes) {
            this.prizes = JSON.parse(savedPrizes);
        }
        
        this.init();
    }
    
    async init() {
        await this.checkSession();
        this.drawWheel();
        this.setupEventListeners();
        this.updateWinnersHistory();
        this.updateDurationDisplay();
    }
    
    async checkSession() {
        const clientName = sessionStorage.getItem('sheinClientName');
        const adminMode = sessionStorage.getItem('sheinAdminMode');

        if (!clientName && !adminMode) {
            // Redirect to registration if no session
            window.location.href = 'register.html';
            return;
        }

        if (adminMode === 'true') {
            this.isAdminMode = true;
            this.currentClient = 'Admin';
            document.getElementById('clientNameDisplay').textContent = 'Admin';
            document.getElementById('toggleAdmin').style.display = 'inline-block';

            // Show admin panel and hide wheel
            document.getElementById('adminPanel').style.display = 'block';
            document.getElementById('wheelSection').style.display = 'none';

            this.prizes = this.wheelConfigs.admin.prizes;

            // Initialize dashboard features
            setTimeout(() => {
                this.setupProfessionalDashboard();
                this.setupClientManagement();
                this.setupPrizeManagement();
                this.setupExportFunctionality();
            }, 100);
        } else {
            this.currentClient = clientName;
            document.getElementById('clientNameDisplay').textContent = clientName;

            // Check client data from Excel
            await this.loadClientData(clientName);
        }
    }

    async loadClientData(clientName) {
        try {
            // Check if client exists in database
            const result = await window.dbClient.getClient(clientName);

            if (!result.success || !result.found) {
                // Client not found - show registration required message
                this.showClientNotRegisteredMessage(clientName);
                return;
            }

            // Client found - proceed with loading
            const clientData = result.client;
            clientData.found = true;
            this.clientData = clientData;
            this.clientAttempts = clientData.attempts || 0;

            // Configure wheel based on payment amount
            this.configureWheelForPayment(clientData.paymentAmount);

            // Update UI with client info
            this.updateClientInfo(clientData);

            // Check if client can play
            this.checkPlayEligibility();

        } catch (error) {
            console.error('Error loading client data:', error);
            this.showErrorToUser('Error loading client data. Please try again.');
        }
    }

    showClientNotRegisteredMessage(clientName) {
        const resultDisplay = document.getElementById('resultDisplay');
        resultDisplay.innerHTML = `
            <div class="client-not-registered">
                <div class="error-icon">🚫</div>
                <h3>Access Denied</h3>
                <p><strong>"${clientName}"</strong> is not registered in our system.</p>
                <p>Only registered clients with valid purchases can access the Fortune Wheel.</p>
                <div class="contact-info">
                    <p>📞 Please contact our admin to register your purchase and get access.</p>
                </div>
            </div>
        `;
        document.getElementById('spinBtn').disabled = true;
        document.getElementById('spinBtn').textContent = 'ACCESS DENIED';
    }

    configureWheelForPayment(paymentAmount) {
        const amount = parseFloat(paymentAmount);

        if (amount >= 30 && amount <= 99) {
            this.prizes = [...this.wheelConfigs.tier1.prizes];
            this.currentTier = 'tier1';
        } else if (amount >= 100 && amount <= 200) {
            this.prizes = [...this.wheelConfigs.tier2.prizes];
            this.currentTier = 'tier2';
        } else {
            // Default to tier1 for amounts outside range
            this.prizes = [...this.wheelConfigs.tier1.prizes];
            this.currentTier = 'tier1';
        }

        // Redraw wheel with new configuration
        this.drawWheel();
    }

    updateClientInfo(clientData) {
        const resultDisplay = document.getElementById('resultDisplay');
        const maxAttempts = clientData.hasWon ? 1 : 2;
        const tierName = this.currentTier === 'tier1' ? 'Standard (30-99€)' : 'Premium (100-200€)';

        resultDisplay.innerHTML = `
            <div class="client-info">
                <div class="client-details">
                    <div class="spending-info">
                        <strong>💰 Spending Amount:</strong> €${clientData.paymentAmount}
                    </div>
                    <div class="tier-info">
                        <strong>🎯 Tier:</strong> ${tierName}
                    </div>
                    <div class="attempts-info">
                        <strong>🎮 Attempts:</strong> ${clientData.attempts}/${maxAttempts}
                        ${clientData.hasWon ? ' (Winner!)' : ''}
                    </div>
                    ${clientData.lastPrize ? `
                        <div class="last-prize-info">
                            <strong>🏆 Last Prize:</strong> ${clientData.lastPrize}
                        </div>
                        <div class="last-date-info">
                            <strong>📅 Date:</strong> ${clientData.lastWinDate}
                        </div>
                    ` : ''}
                </div>
                ${!clientData.hasWon && clientData.attempts < maxAttempts ?
                    '<div class="ready-message">Ready to spin! Good luck! 🍀</div>' :
                    clientData.hasWon ?
                        '<div class="winner-message">🎉 Congratulations! You have won a prize! 🎉</div>' :
                        '<div class="no-attempts-message">No more attempts available. Thank you for playing!</div>'
                }
            </div>
        `;
    }

    checkPlayEligibility() {
        const spinButton = document.getElementById('spinBtn');

        // Check if client has already won
        if (this.clientData && this.clientData.hasWon) {
            spinButton.disabled = true;
            spinButton.textContent = 'ALREADY WON!';
            document.getElementById('resultDisplay').innerHTML =
                '<div class="already-won">Congratulations! You have already won a prize. Thank you for playing!</div>';
            return;
        }

        if (this.clientAttempts >= 2) {
            // Client has used all attempts
            spinButton.disabled = true;
            spinButton.textContent = 'NO MORE ATTEMPTS';
            document.getElementById('resultDisplay').innerHTML =
                '<div class="no-attempts">You have used all your attempts. Thank you for playing!</div>';
        } else if (this.clientAttempts === 1) {
            // Client is on their second (final) attempt
            spinButton.textContent = 'FINAL SPIN!';
            document.getElementById('resultDisplay').innerHTML =
                '<div class="final-attempt">This is your final attempt. Good luck! 🍀</div>';
        } else {
            // Client's first attempt
            spinButton.textContent = 'SPIN THE WHEEL';
        }
    }

    showClientNotFoundMessage() {
        const resultDisplay = document.getElementById('resultDisplay');
        resultDisplay.innerHTML = `
            <div class="client-not-found">
                <strong>Client not found in system!</strong><br>
                Please contact admin to add your payment information.
            </div>
        `;
        document.getElementById('spinBtn').disabled = true;
        document.getElementById('spinBtn').textContent = 'CONTACT ADMIN';
    }

    showErrorMessage(message) {
        const resultDisplay = document.getElementById('resultDisplay');
        resultDisplay.innerHTML = `<div class="error-message">${message}</div>`;
        document.getElementById('spinBtn').disabled = true;
    }

    setupEventListeners() {
        document.getElementById('spinBtn').addEventListener('click', () => this.spin());
        document.getElementById('backToRegister').addEventListener('click', () => this.backToRegister());
        document.getElementById('closeModal').addEventListener('click', () => this.closeWinnerModal());
        
        // Admin controls
        if (this.isAdminMode) {
            // Excel management
            document.getElementById('uploadExcel').addEventListener('click', () => this.uploadExcel());
            document.getElementById('downloadExcel').addEventListener('click', () => this.downloadExcel());
            document.getElementById('createSampleExcel').addEventListener('click', () => this.createSampleExcel());
            document.getElementById('addClient').addEventListener('click', () => this.addClientManually());
            document.getElementById('excelUpload').addEventListener('change', (e) => this.handleExcelUpload(e));

            // Tier management
            document.getElementById('resetTiers').addEventListener('click', () => this.resetTiers());
            document.getElementById('saveTiers').addEventListener('click', () => this.saveTiers());
            document.getElementById('resetWheel').addEventListener('click', () => this.resetWheel());
            document.getElementById('exportData').addEventListener('click', () => this.exportData());
            document.getElementById('exportServerData').addEventListener('click', () => this.exportServerData());
            document.getElementById('exportCSV').addEventListener('click', () => this.exportServerDataFormat('csv'));
            document.getElementById('exportJSON').addEventListener('click', () => this.exportServerDataFormat('json'));
            document.getElementById('loadServerData').addEventListener('click', () => this.loadServerData());
            document.getElementById('clearHistory').addEventListener('click', () => this.clearHistory());
            document.getElementById('spinDuration').addEventListener('input', () => this.updateDurationDisplay());

            // Initialize database and sync
            this.initializeDatabase();

            // Setup professional dashboard
            this.setupProfessionalDashboard();
            this.setupClientManagement();
            this.setupPrizeManagement();
            this.setupExportFunctionality();
        }
        
        const toggleAdminBtn = document.getElementById('toggleAdmin');
        if (toggleAdminBtn) {
            toggleAdminBtn.addEventListener('click', () => this.toggleAdminPanel());
        }

        // Close admin panel button
        const closeAdminBtn = document.getElementById('closeAdmin');
        if (closeAdminBtn) {
            closeAdminBtn.addEventListener('click', () => this.toggleAdminPanel());
        }
        
        // Close modal when clicking outside
        document.getElementById('winnerModal').addEventListener('click', (e) => {
            if (e.target.id === 'winnerModal') {
                this.closeWinnerModal();
            }
        });
    }

    // Error Handling System
    setupErrorHandling() {
        // Global error handler for unhandled errors
        window.addEventListener('error', (event) => {
            this.handleGlobalError('JavaScript Error', event.error, event);
        });

        // Global handler for unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError('Unhandled Promise Rejection', event.reason, event);
            event.preventDefault(); // Prevent default browser error handling
        });

        // Canvas error handling
        if (this.canvas) {
            this.canvas.addEventListener('error', (event) => {
                this.handleCanvasError('Canvas Error', event);
            });
        }
    }

    handleGlobalError(type, error, event) {
        console.error(`${type}:`, error);

        // Log error details
        const errorInfo = {
            type: type,
            message: error?.message || 'Unknown error',
            stack: error?.stack || 'No stack trace',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // Store error in localStorage for debugging
        try {
            const errors = JSON.parse(localStorage.getItem('wheelErrors') || '[]');
            errors.push(errorInfo);
            // Keep only last 10 errors
            if (errors.length > 10) errors.shift();
            localStorage.setItem('wheelErrors', JSON.stringify(errors));
        } catch (e) {
            console.error('Failed to store error:', e);
        }

        // Show user-friendly error message
        this.showErrorToUser('Something went wrong. The page will try to recover automatically.');

        // Attempt recovery
        setTimeout(() => {
            this.attemptRecovery();
        }, 2000);
    }

    handleCanvasError(type, event) {
        console.error(`${type}:`, event);
        this.showErrorToUser('Wheel display error. Refreshing...');

        setTimeout(() => {
            try {
                this.drawWheel();
            } catch (error) {
                this.handleCriticalError('Failed to recover wheel display', error);
            }
        }, 1000);
    }

    handleCriticalError(message, error) {
        console.error('Critical Error:', message, error);

        const errorDisplay = document.getElementById('resultDisplay') || document.body;
        errorDisplay.innerHTML = `
            <div class="critical-error">
                <div class="error-icon">⚠️</div>
                <h3>Application Error</h3>
                <p>${message}</p>
                <p>Please refresh the page to continue.</p>
                <div class="error-actions">
                    <button onclick="window.location.reload()" class="btn-primary">🔄 Refresh Page</button>
                    <button onclick="wheel.clearErrorData()" class="btn-secondary">🗑️ Clear Data & Refresh</button>
                </div>
            </div>
        `;
    }

    showErrorToUser(message) {
        // Create or update error notification
        let errorNotification = document.getElementById('errorNotification');
        if (!errorNotification) {
            errorNotification = document.createElement('div');
            errorNotification.id = 'errorNotification';
            errorNotification.className = 'error-notification';
            document.body.appendChild(errorNotification);
        }

        errorNotification.innerHTML = `
            <div class="error-content">
                <span class="error-icon">⚠️</span>
                <span class="error-message">${message}</span>
                <button onclick="this.parentElement.parentElement.style.display='none'" class="error-close">×</button>
            </div>
        `;

        errorNotification.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorNotification) {
                errorNotification.style.display = 'none';
            }
        }, 5000);
    }

    attemptRecovery() {
        try {
            console.log('Attempting automatic recovery...');

            // Check if essential elements exist
            if (!document.getElementById('wheelCanvas')) {
                throw new Error('Canvas element missing');
            }

            // Reinitialize canvas if needed
            if (!this.ctx) {
                this.canvas = document.getElementById('wheelCanvas');
                this.ctx = this.canvas.getContext('2d');
            }

            // Redraw wheel
            if (this.prizes && this.prizes.length > 0) {
                this.drawWheel();
            }

            // Re-enable spin button if disabled
            const spinBtn = document.getElementById('spinBtn');
            if (spinBtn && this.isSpinning) {
                this.isSpinning = false;
                spinBtn.disabled = false;
                spinBtn.textContent = 'SPIN THE WHEEL';
            }

            console.log('Recovery successful');
            this.showSuccessMessage('Application recovered successfully!');

        } catch (error) {
            console.error('Recovery failed:', error);
            this.handleCriticalError('Automatic recovery failed', error);
        }
    }

    showSuccessMessage(message) {
        let successNotification = document.getElementById('successNotification');
        if (!successNotification) {
            successNotification = document.createElement('div');
            successNotification.id = 'successNotification';
            successNotification.className = 'success-notification';
            document.body.appendChild(successNotification);
        }

        successNotification.innerHTML = `
            <div class="success-content">
                <span class="success-icon">✅</span>
                <span class="success-message">${message}</span>
            </div>
        `;

        successNotification.style.display = 'block';

        setTimeout(() => {
            if (successNotification) {
                successNotification.style.display = 'none';
            }
        }, 3000);
    }

    clearErrorData() {
        try {
            localStorage.removeItem('wheelErrors');
            localStorage.removeItem('sheinClients');
            localStorage.removeItem('wheelConfigs');
            alert('All data cleared. Page will refresh.');
            window.location.reload();
        } catch (error) {
            console.error('Failed to clear data:', error);
            window.location.reload();
        }
    }

    // Database Initialization and Synchronization
    async initializeDatabase() {
        try {
            // Show connection status
            if (window.dbClient) {
                window.dbClient.showConnectionStatus();

                // Check if we have local data to migrate to cloud
                const localClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
                const localWinners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

                if (localClients.length > 0 || localWinners.length > 0) {
                    console.log('Found local data, attempting to sync with cloud database...');

                    // Show sync notification
                    this.showSyncNotification();

                    // Attempt to sync local data to cloud database
                    try {
                        const syncResult = await this.syncLocalToCloud();

                        if (syncResult.success) {
                            console.log('Local data synced successfully to cloud');
                            this.showSuccessMessage('Local data synced to cloud database!');
                        } else {
                            console.warn('Failed to sync local data:', syncResult.error);
                        }
                    } catch (error) {
                        console.warn('Sync failed, continuing with local storage');
                    }
                }
            }
        } catch (error) {
            console.error('Error initializing database:', error);
        }
    }

    showSyncNotification() {
        let syncNotification = document.getElementById('syncNotification');
        if (!syncNotification) {
            syncNotification = document.createElement('div');
            syncNotification.id = 'syncNotification';
            syncNotification.className = 'sync-notification';
            document.body.appendChild(syncNotification);
        }

        syncNotification.innerHTML = `
            <div class="sync-content">
                <span class="sync-icon">🔄</span>
                <span class="sync-message">Syncing local data to cloud database...</span>
            </div>
        `;

        syncNotification.style.display = 'block';

        setTimeout(() => {
            if (syncNotification) {
                syncNotification.style.display = 'none';
            }
        }, 3000);
    }

    // Sync local data to cloud database
    async syncLocalToCloud() {
        try {
            const localClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
            const localWinners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

            let synced = 0;
            let errors = 0;

            // Sync clients
            for (const client of localClients) {
                try {
                    const result = await window.dbClient.addClient(
                        client.clientName,
                        client.paymentAmount,
                        client.attempts || 0,
                        client.hasWon || false
                    );

                    if (result.success) {
                        synced++;
                    } else if (result.error === 'Client already exists') {
                        // Update existing client with local data
                        await window.dbClient.updateClient(client.clientName, {
                            paymentAmount: client.paymentAmount,
                            attempts: client.attempts || 0,
                            hasWon: client.hasWon || false,
                            lastPrize: client.lastPrize || '',
                            lastWinDate: client.lastWinDate || '',
                            totalWinnings: client.totalWinnings || 0
                        });
                        synced++;
                    }
                } catch (error) {
                    console.error('Error syncing client:', client.clientName, error);
                    errors++;
                }
            }

            // Sync winners
            for (const winner of localWinners) {
                try {
                    await window.dbClient.addWinner(
                        winner.clientName,
                        winner.prize,
                        winner.timestamp
                    );
                } catch (error) {
                    console.error('Error syncing winner:', winner, error);
                    errors++;
                }
            }

            return {
                success: errors === 0,
                synced,
                errors,
                message: `Synced ${synced} items with ${errors} errors`
            };
        } catch (error) {
            console.error('Error during sync:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 404 Handling System
    setup404Handling() {
        // Check for missing resources
        this.checkRequiredResources();

        // Handle navigation errors
        window.addEventListener('popstate', (event) => {
            this.handleNavigation();
        });

        // Check for broken links
        document.addEventListener('click', (event) => {
            if (event.target.tagName === 'A') {
                this.validateLink(event.target);
            }
        });
    }

    checkRequiredResources() {
        const requiredElements = [
            'wheelCanvas',
            'spinBtn',
            'resultDisplay'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));

        if (missingElements.length > 0) {
            console.warn('Missing required elements:', missingElements);
            this.handleMissingElements(missingElements);
        }

        // Check for required images
        this.checkRequiredImages();
    }

    checkRequiredImages() {
        const requiredImages = ['shein-logo.png'];

        requiredImages.forEach(imagePath => {
            const img = new Image();
            img.onerror = () => {
                console.warn(`Missing image: ${imagePath}`);
                this.handleMissingImage(imagePath);
            };
            img.src = imagePath;
        });
    }

    handleMissingElements(missingElements) {
        const errorMessage = `Missing page elements: ${missingElements.join(', ')}`;

        // Show error to user
        this.showErrorToUser('Page elements are missing. This might not be the correct page.');

        // Suggest navigation to correct page
        setTimeout(() => {
            if (confirm('This page seems incomplete. Would you like to go to the main page?')) {
                window.location.href = '/register.html';
            }
        }, 3000);
    }

    handleMissingImage(imagePath) {
        // Replace missing images with placeholder
        const images = document.querySelectorAll(`img[src="${imagePath}"]`);
        images.forEach(img => {
            img.style.display = 'none';
            const placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.textContent = '🎯';
            placeholder.style.cssText = `
                width: ${img.width || 50}px;
                height: ${img.height || 50}px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f0f0f0;
                border-radius: 50%;
                font-size: 24px;
            `;
            img.parentNode.insertBefore(placeholder, img);
        });
    }

    handleNavigation() {
        // Check if current page is valid
        const currentPath = window.location.pathname;
        const validPaths = [
            '/',
            '/index.html',
            '/register.html',
            '/wheel.html',
            '/404.html'
        ];

        if (!validPaths.includes(currentPath) && !currentPath.endsWith('.html')) {
            console.warn('Invalid path detected:', currentPath);
            this.redirect404();
        }
    }

    validateLink(linkElement) {
        const href = linkElement.getAttribute('href');
        if (!href) return;

        // Check if it's an internal link
        if (href.startsWith('/') || href.startsWith('./') || !href.includes('://')) {
            // Validate internal links
            if (href.includes('.html')) {
                this.checkPageExists(href).then(exists => {
                    if (!exists) {
                        console.warn('Broken link detected:', href);
                        linkElement.style.color = '#ff6b6b';
                        linkElement.title = 'This link may be broken';
                    }
                });
            }
        }
    }

    async checkPageExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    redirect404() {
        // Show 404 message
        this.show404Message();

        // Redirect after delay
        setTimeout(() => {
            window.location.href = '/404.html';
        }, 3000);
    }

    show404Message() {
        const message404 = document.createElement('div');
        message404.className = 'message-404';
        message404.innerHTML = `
            <div class="message-404-content">
                <div class="message-404-icon">🔍</div>
                <h3>Page Not Found</h3>
                <p>Redirecting to error page...</p>
            </div>
        `;

        message404.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const content = message404.querySelector('.message-404-content');
        content.style.cssText = `
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(message404);
    }

    drawWheel() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        if (this.prizes.length === 0) {
            this.ctx.fillStyle = '#f0f0f0';
            this.ctx.beginPath();
            this.ctx.arc(this.centerX, this.centerY, this.radius, 0, 2 * Math.PI);
            this.ctx.fill();

            this.ctx.fillStyle = '#666';
            this.ctx.font = '24px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('Add prizes to start', this.centerX, this.centerY);
            return;
        }

        // Equal segments regardless of winning chances
        const numberOfPrizes = this.prizes.length;
        const sliceAngle = (2 * Math.PI) / numberOfPrizes;
        // Start from -90 degrees (top of the wheel) to align with pointer
        let currentAngle = -Math.PI / 2;

        this.prizes.forEach((prize, index) => {
            // Draw slice
            this.ctx.beginPath();
            this.ctx.moveTo(this.centerX, this.centerY);
            this.ctx.arc(this.centerX, this.centerY, this.radius, currentAngle, currentAngle + sliceAngle);
            this.ctx.closePath();
            this.ctx.fillStyle = prize.color;
            this.ctx.fill();

            // Draw border
            this.ctx.strokeStyle = '#fff';
            this.ctx.lineWidth = 4;
            this.ctx.stroke();

            // Draw text with better readability
            const textAngle = currentAngle + sliceAngle / 2;
            const textRadius = this.radius * 0.75;
            const textX = this.centerX + Math.cos(textAngle) * textRadius;
            const textY = this.centerY + Math.sin(textAngle) * textRadius;

            this.ctx.save();
            this.ctx.translate(textX, textY);
            this.ctx.rotate(textAngle + Math.PI / 2);

            // Consistent font size for equal segments
            const maxTextLength = 15;
            let fontSize = Math.min(18, Math.max(12, 120 / numberOfPrizes));
            if (prize.name.length > maxTextLength) {
                fontSize = Math.max(10, fontSize * 0.8);
            }

            this.ctx.font = `bold ${fontSize}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // Add text shadow for better contrast
            this.ctx.strokeStyle = '#000';
            this.ctx.lineWidth = 4;
            this.ctx.strokeText(prize.name, 0, 0);

            // Add prize name display (no percentages on wheel)
            this.ctx.fillStyle = '#fff';
            this.ctx.fillText(prize.name, 0, 0);

            this.ctx.restore();

            currentAngle += sliceAngle;
        });
    }
    
    spin() {
        try {
            if (this.isSpinning || this.prizes.length === 0) return;

            // Check if client can play (for non-admin users)
            if (!this.isAdminMode) {
                if (this.clientData && this.clientData.hasWon) {
                    alert('You have already won a prize!');
                    return;
                }
                if (this.clientAttempts >= 2) {
                    alert('You have used all your attempts!');
                    return;
                }
            }

            this.isSpinning = true;
            const spinBtn = document.getElementById('spinBtn');
            if (spinBtn) {
                spinBtn.disabled = true;
            }

            const duration = this.isAdminMode ?
                parseFloat(document.getElementById('spinDuration').value) * 1000 : 3000;
            const minRotations = 15;
            const maxRotations = 25;
            const rotations = minRotations + Math.random() * (maxRotations - minRotations);
            const finalRotation = rotations * 360;

            // Determine winner based on weighted chances
            const winner = this.selectWinner();
            if (!winner) {
                throw new Error('Failed to select winner');
            }

            const winnerIndex = this.prizes.indexOf(winner);
            if (winnerIndex === -1) {
                throw new Error('Winner not found in prizes array');
            }

            // Calculate target angle for equal segments
            const numberOfPrizes = this.prizes.length;
            const segmentAngle = 360 / numberOfPrizes;

            // Target angle is the center of the winning segment
            const targetAngle = (winnerIndex * segmentAngle) + (segmentAngle / 2);

            // Adjust for pointer position at top
            const adjustedTargetAngle = targetAngle - 90;

            // Calculate final angle to land on the target
            const finalAngle = finalRotation - adjustedTargetAngle;

            this.animateWheel(finalAngle, duration, winner);

        } catch (error) {
            console.error('Error during spin:', error);
            this.isSpinning = false;
            const spinBtn = document.getElementById('spinBtn');
            if (spinBtn) {
                spinBtn.disabled = false;
            }
            this.showErrorToUser('Failed to spin the wheel. Please try again.');
        }
    }
    
    selectWinner() {
        const totalChance = this.prizes.reduce((sum, prize) => sum + prize.chance, 0);
        const random = Math.random() * totalChance;
        
        let currentChance = 0;
        for (const prize of this.prizes) {
            currentChance += prize.chance;
            if (random <= currentChance) {
                return prize;
            }
        }
        return this.prizes[0];
    }
    
    animateWheel(finalAngle, duration, winner) {
        const startTime = Date.now();
        const startRotation = this.currentRotation;
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Enhanced easing function for more realistic deceleration
            const easeOut = 1 - Math.pow(1 - progress, 4);
            
            this.currentRotation = startRotation + finalAngle * easeOut;
            
            this.ctx.save();
            this.ctx.translate(this.centerX, this.centerY);
            this.ctx.rotate((this.currentRotation * Math.PI) / 180);
            this.ctx.translate(-this.centerX, -this.centerY);
            this.drawWheel();
            this.ctx.restore();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.onSpinComplete(winner);
            }
        };
        
        animate();
    }
    
    async onSpinComplete(winner) {
        this.isSpinning = false;

        // Increment attempts for non-admin users
        if (!this.isAdminMode) {
            this.clientAttempts++;
        }

        const isLosingPrize = winner.name.includes('Try Again') || winner.name.includes('Better Luck');

        // Add celebration effect for winning prizes
        if (!isLosingPrize) {
            this.showCelebration();
        }

        // Display result with animation
        const resultDisplay = document.getElementById('resultDisplay');

        if (isLosingPrize) {
            resultDisplay.style.background = 'linear-gradient(45deg, #FF6347, #FFA500)';
            resultDisplay.style.color = 'white';
            resultDisplay.style.transform = 'scale(1.05)';

            if (this.clientAttempts < 2 && !this.isAdminMode) {
                resultDisplay.innerHTML = `😔 ${winner.name}! You get one more chance!`;
            } else {
                resultDisplay.innerHTML = `😔 ${winner.name}! Thank you for playing!`;
            }
        } else {
            resultDisplay.style.background = 'linear-gradient(45deg, #FF6BB7, #4ECDC4)';
            resultDisplay.style.color = 'white';
            resultDisplay.style.transform = 'scale(1.05)';
            resultDisplay.innerHTML =
                `🎉 ${this.currentClient} won: <strong>${winner.name}</strong>! 🎉`;

            // Show winner popup modal for actual prizes
            this.showWinnerModal(this.currentClient, winner.name);
        }

        // Update Excel file with the result
        if (!this.isAdminMode) {
            await this.updateExcelWithResult(winner.name);
        }

        // Save to history (both local and server)
        const winRecord = {
            client: this.currentClient,
            prize: winner.name,
            timestamp: new Date().toLocaleString(),
            isLoss: isLosingPrize
        };

        this.winners.push(winRecord);
        localStorage.setItem('sheinWinners', JSON.stringify(this.winners));

        // Save to server
        this.saveToServer(winRecord);

        // Update button state based on attempts
        if (!this.isAdminMode) {
            this.updateButtonAfterSpin(isLosingPrize);
        } else {
            document.getElementById('spinBtn').disabled = false;
        }

        // Reset result display style after delay
        setTimeout(() => {
            resultDisplay.style.background = '#f8f9fa';
            resultDisplay.style.color = '#333';
            resultDisplay.style.transform = 'scale(1)';

            if (!this.isAdminMode) {
                this.updateClientInfo(this.clientData);
            } else {
                resultDisplay.innerHTML = 'Ready to spin again! 🍀';
            }
        }, 5000);

        if (this.isAdminMode) {
            this.updateWinnersHistory();
        }
    }
    
    showWinnerModal(clientName, prizeName) {
        document.getElementById('modalWinnerName').textContent = clientName;
        document.getElementById('modalWinnerPrize').textContent = prizeName;
        document.getElementById('winnerModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
    
    closeWinnerModal() {
        document.getElementById('winnerModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    async updateExcelWithResult(prize) {
        try {
            // Prepare updates
            const updates = {
                attempts: (this.clientData.attempts || 0) + 1,
                lastPrize: prize,
                lastWinDate: new Date().toLocaleString()
            };

            // Only increment total winnings if it's not a losing prize
            if (!prize.includes('Try Again') && !prize.includes('Better Luck')) {
                updates.totalWinnings = (this.clientData.totalWinnings || 0) + 1;
                updates.hasWon = true;

                // Add winner record
                await window.dbClient.addWinner(this.currentClient, prize);
            }

            // Update client data in database
            const result = await window.dbClient.updateClient(this.currentClient, updates);

            if (result.success) {
                // Update local client data
                this.clientData = result.client;
                console.log('Client data updated successfully:', this.clientData);
            } else {
                console.error('Failed to update client data:', result.error);
                this.showErrorToUser('Failed to save game result. Please try again.');
            }
        } catch (error) {
            console.error('Error updating client data:', error);
            this.showErrorToUser('Error saving game result.');
        }
    }

    updateButtonAfterSpin(isLosingPrize) {
        const spinButton = document.getElementById('spinBtn');

        if (!isLosingPrize) {
            // Winner gets no more attempts - game over immediately
            spinButton.disabled = true;
            spinButton.textContent = 'CONGRATULATIONS!';

            // Update client data to mark as having won (no more attempts)
            this.markClientAsWinner();
        } else if (this.clientAttempts >= 2) {
            // Loser has used both attempts
            spinButton.disabled = true;
            spinButton.textContent = 'GAME OVER';
        } else if (this.clientAttempts === 1) {
            // Loser gets one more chance
            spinButton.disabled = false;
            spinButton.textContent = 'FINAL CHANCE!';
        }
    }

    async markClientAsWinner() {
        try {
            // Update client data to prevent further attempts
            const updates = {
                hasWon: true,
                attempts: 999 // Mark as completed
            };

            const result = await window.dbClient.updateClient(this.currentClient, updates);

            if (result.success) {
                this.clientData = result.client;
                console.log('Client marked as winner:', this.clientData);
            } else {
                console.error('Failed to mark client as winner:', result.error);
            }
        } catch (error) {
            console.error('Error marking client as winner:', error);
        }
    }

    showCelebration() {
        const wheel = document.getElementById('wheelCanvas');
        wheel.style.boxShadow = '0 0 40px #FF6BB7, 0 0 80px #4ECDC4';

        setTimeout(() => {
            wheel.style.boxShadow = '0 15px 50px rgba(0,0,0,0.3)';
        }, 2000);
    }
    
    backToRegister() {
        sessionStorage.removeItem('sheinClientName');
        sessionStorage.removeItem('sheinAdminMode');
        window.location.href = 'register.html';
    }
    
    toggleAdminPanel() {
        const panel = document.getElementById('adminPanel');
        const wheelSection = document.getElementById('wheelSection');
        const button = document.getElementById('toggleAdmin');

        if (panel.style.display === 'none') {
            // Show admin panel, hide wheel
            panel.style.display = 'block';
            wheelSection.style.display = 'none';
            button.textContent = 'Close Dashboard';
            button.className = 'btn-danger';
            this.isAdminMode = true;

            // Initialize dashboard features
            this.setupProfessionalDashboard();
            this.setupClientManagement();
            this.setupPrizeManagement();
            this.setupExportFunctionality();
        } else {
            // Hide admin panel, show wheel
            panel.style.display = 'none';
            wheelSection.style.display = 'block';
            button.textContent = 'Admin Panel';
            button.className = 'btn-admin';
            this.isAdminMode = false;
        }
    }
    
    // Admin functions (same as before but with save functionality)
    addPrize() {
        const name = document.getElementById('prizeName').value.trim();
        const chance = parseInt(document.getElementById('prizeChance').value);
        const color = document.getElementById('prizeColor').value;
        
        if (!name || !chance || chance < 1 || chance > 100) {
            alert('Please enter valid prize details');
            return;
        }
        
        this.prizes.push({ name, chance, color });
        this.savePrizes();
        this.drawWheel();
        this.updatePrizesList();
        
        // Clear form
        document.getElementById('prizeName').value = '';
        document.getElementById('prizeChance').value = '10';
        document.getElementById('prizeColor').value = '#FF6BB7';
    }
    
    removePrize(index) {
        this.prizes.splice(index, 1);
        this.savePrizes();
        this.drawWheel();
        this.updatePrizesList();
    }
    
    updatePrizesList() {
        if (!this.isAdminMode) return;
        
        const container = document.getElementById('prizesList');
        container.innerHTML = '';
        
        this.prizes.forEach((prize, index) => {
            const prizeElement = document.createElement('div');
            prizeElement.className = 'prize-item';
            prizeElement.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <div class="prize-color" style="background-color: ${prize.color}"></div>
                    <div class="prize-info">
                        <strong>${prize.name}</strong> (${prize.chance}%)
                    </div>
                </div>
                <div class="prize-actions">
                    <button class="btn-secondary" onclick="wheel.editPrize(${index})">Edit</button>
                    <button class="btn-danger" onclick="wheel.removePrize(${index})">Remove</button>
                </div>
            `;
            container.appendChild(prizeElement);
        });
    }
    
    editPrize(index) {
        const prize = this.prizes[index];
        const newName = prompt('Enter new prize name:', prize.name);
        if (newName === null) return;
        
        const newChance = prompt('Enter new winning chance (1-100):', prize.chance);
        if (newChance === null) return;
        
        const chance = parseInt(newChance);
        if (isNaN(chance) || chance < 1 || chance > 100) {
            alert('Please enter a valid chance between 1 and 100');
            return;
        }
        
        const newColor = prompt('Enter new color (hex code):', prize.color);
        if (newColor === null) return;
        
        this.prizes[index] = {
            name: newName.trim() || prize.name,
            chance: chance,
            color: newColor.trim() || prize.color
        };
        
        this.savePrizes();
        this.drawWheel();
        this.updatePrizesList();
    }
    
    resetWheel() {
        this.currentRotation = 0;
        this.drawWheel();
        document.getElementById('resultDisplay').innerHTML = 'Ready to spin! 🍀';
    }
    
    updateWinnersHistory() {
        if (!this.isAdminMode) return;
        
        const container = document.getElementById('historyList');
        container.innerHTML = '';
        
        this.winners.slice(-10).reverse().forEach(winner => {
            const historyElement = document.createElement('div');
            historyElement.className = 'history-item';
            historyElement.innerHTML = `
                <div><strong>${winner.client}</strong> won <em>${winner.prize}</em></div>
                <div class="timestamp">${winner.timestamp}</div>
            `;
            container.appendChild(historyElement);
        });
    }
    
    exportData() {
        if (this.isAdminMode) {
            // Try server export first
            this.exportServerData();
        } else {
            // Local export for non-admin users
            const data = this.winners.map(w =>
                `${w.timestamp} - ${w.client}: ${w.prize}`
            ).join('\n');

            const blob = new Blob([data], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'shein_winners.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
    }
    
    clearHistory() {
        if (confirm('Are you sure you want to clear all winner history?')) {
            this.winners = [];
            localStorage.removeItem('sheinWinners');
            this.updateWinnersHistory();
        }
    }
    
    updateDurationDisplay() {
        if (!this.isAdminMode) return;
        const duration = document.getElementById('spinDuration').value;
        document.getElementById('durationValue').textContent = duration;
    }
    
    savePrizes() {
        localStorage.setItem('sheinPrizes', JSON.stringify(this.prizes));
    }

    async saveToServer(winRecord) {
        try {
            const response = await fetch('/.netlify/functions/save-winner', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientName: winRecord.client,
                    prize: winRecord.prize,
                    timestamp: winRecord.timestamp,
                    clientInfo: {
                        userAgent: navigator.userAgent,
                        timestamp: new Date().toISOString(),
                        sessionId: sessionStorage.getItem('sessionId') || this.generateSessionId()
                    }
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Winner data saved to server:', result);
            } else {
                console.error('Failed to save to server:', response.statusText);
            }
        } catch (error) {
            console.error('Error saving to server:', error);
            // Fallback to local storage only
        }
    }

    generateSessionId() {
        const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('sessionId', sessionId);
        return sessionId;
    }

    async exportServerData() {
        try {
            const response = await fetch('/.netlify/functions/export-data?type=all&format=txt');
            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'shein-complete-export.txt';
                a.click();
                URL.revokeObjectURL(url);
            } else {
                throw new Error('Failed to export data from server');
            }
        } catch (error) {
            console.error('Error exporting server data:', error);
            alert('Failed to export server data. Exporting local data instead.');
            this.exportData();
        }
    }

    async exportServerDataFormat(format) {
        try {
            const response = await fetch(`/.netlify/functions/export-data?type=all&format=${format}`);
            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `shein-complete-export.${format}`;
                a.click();
                URL.revokeObjectURL(url);
            } else {
                throw new Error(`Failed to export ${format} data from server`);
            }
        } catch (error) {
            console.error(`Error exporting ${format} data:`, error);
            alert(`Failed to export ${format} data from server.`);
        }
    }

    async loadServerData() {
        try {
            const response = await fetch('/.netlify/functions/get-winners');
            if (response.ok) {
                const data = await response.json();

                // Update local storage with server data
                if (data.winners && data.winners.length > 0) {
                    this.winners = data.winners;
                    localStorage.setItem('sheinWinners', JSON.stringify(this.winners));
                    this.updateWinnersHistory();
                    this.updateDataStats();
                    alert(`Loaded ${data.winners.length} winners from server.`);
                } else {
                    alert('No server data found.');
                }
            } else {
                throw new Error('Failed to load data from server');
            }
        } catch (error) {
            console.error('Error loading server data:', error);
            alert('Failed to load data from server.');
        }
    }

    async updateDataStats() {
        if (!this.isAdminMode) return;

        try {
            const response = await fetch('/.netlify/functions/get-winners');
            if (response.ok) {
                const data = await response.json();
                const statsElement = document.getElementById('dataStats');

                if (statsElement) {
                    statsElement.innerHTML = `
                        <div class="stats-grid">
                            <div class="stat-item">
                                <strong>Server Winners:</strong> ${data.totalWinners || 0}
                            </div>
                            <div class="stat-item">
                                <strong>Server Clients:</strong> ${data.totalClients || 0}
                            </div>
                            <div class="stat-item">
                                <strong>Local Winners:</strong> ${this.winners.length}
                            </div>
                            <div class="stat-item">
                                <strong>Last Updated:</strong> ${new Date().toLocaleString()}
                            </div>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Error updating data stats:', error);
        }
    }

    // Excel Management Methods
    uploadExcel() {
        document.getElementById('excelUpload').click();
    }

    async handleExcelUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
            alert('Please select an Excel file (.xlsx or .xls)');
            return;
        }

        try {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // Get the first worksheet
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length === 0) {
                        alert('The Excel file appears to be empty.');
                        return;
                    }

                    this.importClientsFromData(jsonData);

                } catch (parseError) {
                    console.error('Error parsing Excel file:', parseError);
                    alert('Error parsing Excel file. Please make sure it\'s a valid Excel file with client data.');
                }
            };

            reader.readAsArrayBuffer(file);

        } catch (error) {
            console.error('Error reading Excel file:', error);
            alert('Error reading Excel file');
        }
    }

    importClientsFromData(jsonData) {
        try {
            const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
            let importedCount = 0;
            let updatedCount = 0;
            let skippedCount = 0;

            jsonData.forEach((row, index) => {
                // Try to find client name and payment amount in various column formats
                const clientName = row['Client Name'] || row['Name'] || row['client_name'] || row['name'] ||
                                 row['ClientName'] || row['CLIENT_NAME'] || row['Client'] || row['CLIENT'];

                const paymentAmount = parseFloat(
                    row['Payment Amount'] || row['Amount'] || row['payment_amount'] || row['amount'] ||
                    row['PaymentAmount'] || row['PAYMENT_AMOUNT'] || row['Payment'] || row['PAYMENT'] ||
                    row['Spending'] || row['spending'] || row['Total'] || row['total']
                );

                if (!clientName || !paymentAmount || paymentAmount < 30 || paymentAmount > 200) {
                    console.warn(`Row ${index + 1}: Invalid data - Name: "${clientName}", Amount: ${paymentAmount}`);
                    skippedCount++;
                    return;
                }

                // Check if client already exists
                const existingIndex = storedClients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === clientName.toLowerCase()
                );

                if (existingIndex >= 0) {
                    // Update existing client
                    storedClients[existingIndex].paymentAmount = paymentAmount;
                    updatedCount++;
                } else {
                    // Add new client
                    storedClients.push({
                        clientName: clientName,
                        paymentAmount: paymentAmount,
                        attempts: 0,
                        lastPrize: '',
                        lastWinDate: '',
                        totalWinnings: 0,
                        hasWon: false
                    });
                    importedCount++;
                }
            });

            // Save to localStorage
            localStorage.setItem('sheinClients', JSON.stringify(storedClients));

            // Show results
            const message = `Import completed!\n` +
                           `• New clients added: ${importedCount}\n` +
                           `• Existing clients updated: ${updatedCount}\n` +
                           `• Rows skipped (invalid data): ${skippedCount}\n\n` +
                           `Note: Valid payment amounts must be between 30€ and 200€.`;

            alert(message);
            this.updateExcelStats();

            // Clear the file input
            event.target.value = '';

        } catch (error) {
            console.error('Error importing client data:', error);
            alert('Error importing client data');
        }
    }

    async downloadExcel() {
        try {
            // Get client data from localStorage
            const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
            const winners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

            if (storedClients.length === 0 && winners.length === 0) {
                alert('No data to export');
                return;
            }

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Create clients sheet
            if (storedClients.length > 0) {
                const clientsData = storedClients.map(client => ({
                    'Client Name': client.clientName || '',
                    'Payment Amount': client.paymentAmount || 0,
                    'Attempts': client.attempts || 0,
                    'Last Prize': client.lastPrize || '',
                    'Last Win Date': client.lastWinDate || '',
                    'Total Winnings': client.totalWinnings || 0
                }));

                const clientsWS = XLSX.utils.json_to_sheet(clientsData);
                XLSX.utils.book_append_sheet(wb, clientsWS, 'Clients');
            }

            // Create winners sheet
            if (winners.length > 0) {
                const winnersData = winners.map(winner => ({
                    'Client Name': winner.client || '',
                    'Prize': winner.prize || '',
                    'Date': winner.timestamp || ''
                }));

                const winnersWS = XLSX.utils.json_to_sheet(winnersData);
                XLSX.utils.book_append_sheet(wb, winnersWS, 'Winners');
            }

            // Generate and download file
            const fileName = `shein-wheel-data-${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);

            alert('Data exported successfully!');
        } catch (error) {
            console.error('Error exporting data:', error);
            alert('Error exporting data');
        }
    }

    async createSampleExcel() {
        try {
            // Create sample data
            const sampleClients = [
                { clientName: 'John Doe', paymentAmount: 45 },
                { clientName: 'Jane Smith', paymentAmount: 120 },
                { clientName: 'Mike Johnson', paymentAmount: 75 },
                { clientName: 'Sarah Wilson', paymentAmount: 150 }
            ];

            // Add to localStorage
            const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');

            for (const client of sampleClients) {
                // Check if client already exists
                const existingIndex = storedClients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === client.clientName.toLowerCase()
                );

                if (existingIndex >= 0) {
                    // Update existing client
                    storedClients[existingIndex].paymentAmount = client.paymentAmount;
                } else {
                    // Add new client
                    storedClients.push({
                        clientName: client.clientName,
                        paymentAmount: client.paymentAmount,
                        attempts: 0,
                        lastPrize: '',
                        lastWinDate: '',
                        totalWinnings: 0
                    });
                }
            }

            localStorage.setItem('sheinClients', JSON.stringify(storedClients));

            alert('Sample client data created with 4 sample clients!');
            this.updateExcelStats();
        } catch (error) {
            console.error('Error creating sample data:', error);
            alert('Error creating sample client data');
        }
    }

    async addClientManually() {
        const clientName = document.getElementById('newClientName').value.trim();
        const paymentAmount = parseFloat(document.getElementById('newClientPayment').value);

        if (!clientName || !paymentAmount || paymentAmount < 30 || paymentAmount > 200) {
            alert('Please enter valid client name and payment amount (30-200€)');
            return;
        }

        try {
            this.addClientToStorage(clientName, paymentAmount);

            // Clear form
            document.getElementById('newClientName').value = '';
            document.getElementById('newClientPayment').value = '';

            alert(`Client "${clientName}" added successfully!`);
            this.updateExcelStats();
        } catch (error) {
            console.error('Error adding client:', error);
            alert('Error adding client');
        }
    }

    async addClientToStorage(clientName, paymentAmount) {
        try {
            // Try to add new client to database
            const result = await window.dbClient.addClient(clientName, paymentAmount);

            if (result.success) {
                return { success: true, updated: false, client: result.client };
            } else if (result.error === 'Client already exists') {
                // Update existing client
                const updateResult = await window.dbClient.updateClient(clientName, {
                    paymentAmount: paymentAmount
                });

                if (updateResult.success) {
                    return { success: true, updated: true, client: updateResult.client };
                } else {
                    return { success: false, error: updateResult.error };
                }
            } else {
                return { success: false, error: result.error };
            }
        } catch (error) {
            console.error('Error adding client to storage:', error);
            return { success: false, error: error.message };
        }
    }

    async updateExcelStats() {
        if (!this.isAdminMode) return;

        try {
            const result = await window.dbClient.getAllClients();
            const statsElement = document.getElementById('excelStats');

            if (statsElement && result.success) {
                const storedClients = result.clients;
                const totalClients = storedClients.length;
                const tier1Clients = storedClients.filter(c => c.paymentAmount >= 30 && c.paymentAmount <= 99).length;
                const tier2Clients = storedClients.filter(c => c.paymentAmount >= 100 && c.paymentAmount <= 200).length;
                const winners = storedClients.filter(c => c.hasWon).length;

                // Check connection status
                const connectionStatus = await window.dbClient.checkConnection();
                const statusIcon = connectionStatus.connected ? '🟢' : '🟡';
                const statusText = connectionStatus.connected ? 'Cloud Database' : 'Local Storage';

                statsElement.innerHTML = `
                    <div class="excel-stats-grid">
                        <div class="stat-item">
                            <strong>📊 Database:</strong> ${statusIcon} ${statusText}
                        </div>
                        <div class="stat-item">
                            <strong>Total Clients:</strong> ${totalClients}
                        </div>
                        <div class="stat-item">
                            <strong>Tier 1 (30-99€):</strong> ${tier1Clients}
                        </div>
                        <div class="stat-item">
                            <strong>Tier 2 (100-200€):</strong> ${tier2Clients}
                        </div>
                        <div class="stat-item">
                            <strong>Winners:</strong> ${winners}
                        </div>
                        <div class="stat-item">
                            <strong>Last Updated:</strong> ${new Date().toLocaleString()}
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error updating client stats:', error);
        }
    }

    // Tier Management Functions
    setupTierManagement() {
        if (!this.isAdminMode) return;

        // Load saved configurations if they exist
        const savedConfigs = localStorage.getItem('sheinWheelConfigs');
        if (savedConfigs) {
            this.wheelConfigs = JSON.parse(savedConfigs);
        }

        this.displayTierPrizes('tier1', 'tier1Prizes');
        this.displayTierPrizes('tier2', 'tier2Prizes');
    }

    displayTierPrizes(tierKey, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        const tierPrizes = this.wheelConfigs[tierKey].prizes;

        tierPrizes.forEach((prize, index) => {
            const prizeElement = document.createElement('div');
            prizeElement.className = 'tier-prize-item';
            prizeElement.innerHTML = `
                <div class="prize-info">
                    <span class="prize-name">${prize.name}</span>
                    <input type="number"
                           class="chance-input"
                           value="${prize.chance}"
                           min="1"
                           max="100"
                           data-tier="${tierKey}"
                           data-index="${index}">
                    <span>%</span>
                </div>
            `;
            container.appendChild(prizeElement);
        });
    }

    saveTiers() {
        // Update tier1 chances
        const tier1Inputs = document.querySelectorAll('[data-tier="tier1"]');
        tier1Inputs.forEach((input, index) => {
            const chance = parseInt(input.value);
            if (chance >= 1 && chance <= 100) {
                this.wheelConfigs.tier1.prizes[index].chance = chance;
            }
        });

        // Update tier2 chances
        const tier2Inputs = document.querySelectorAll('[data-tier="tier2"]');
        tier2Inputs.forEach((input, index) => {
            const chance = parseInt(input.value);
            if (chance >= 1 && chance <= 100) {
                this.wheelConfigs.tier2.prizes[index].chance = chance;
            }
        });

        // Save to localStorage
        localStorage.setItem('sheinWheelConfigs', JSON.stringify(this.wheelConfigs));

        // Update current wheel if client is loaded
        if (this.currentTier) {
            this.prizes = [...this.wheelConfigs[this.currentTier].prizes];
            this.drawWheel();
        }

        alert('Tier configurations saved successfully!');
    }

    resetTiers() {
        if (confirm('Reset all tier configurations to default values?')) {
            // Reset to original configurations
            this.wheelConfigs = {
                'tier1': {
                    prizes: [
                        { name: '5€ Gift Card', chance: 25, color: '#FF6BB7' },
                        { name: 'Surprise Gift', chance: 25, color: '#4ECDC4' },
                        { name: '10% Reduction', chance: 25, color: '#FFB6C1' },
                        { name: 'Try Again!', chance: 12.5, color: '#FFA500' },
                        { name: 'Better Luck Next Time', chance: 12.5, color: '#FF6347' }
                    ]
                },
                'tier2': {
                    prizes: [
                        { name: '5€ Gift Card', chance: 20, color: '#FF6BB7' },
                        { name: '10€ Gift Card', chance: 20, color: '#4ECDC4' },
                        { name: 'Surprise Gift', chance: 20, color: '#FFB6C1' },
                        { name: '10% Reduction', chance: 15, color: '#87CEEB' },
                        { name: '20% Reduction', chance: 15, color: '#DDA0DD' },
                        { name: 'Try Again!', chance: 5, color: '#FFA500' },
                        { name: 'Better Luck Next Time', chance: 5, color: '#FF6347' }
                    ]
                },
                'admin': {
                    prizes: [
                        { name: '10€ Gift Card', chance: 25, color: '#FF6BB7' },
                        { name: '20€ Gift Card', chance: 20, color: '#4ECDC4' },
                        { name: 'Free Shipping', chance: 25, color: '#FFB6C1' },
                        { name: '15€ Gift Card', chance: 15, color: '#87CEEB' },
                        { name: '25€ Gift Card', chance: 10, color: '#DDA0DD' },
                        { name: '50€ Gift Card', chance: 5, color: '#98FB98' }
                    ]
                }
            };

            localStorage.removeItem('sheinWheelConfigs');
            this.setupTierManagement();

            if (this.currentTier) {
                this.prizes = [...this.wheelConfigs[this.currentTier].prizes];
                this.drawWheel();
            }

            alert('Tier configurations reset to default!');
        }
    }

    // Client Table Management
    setupClientTable() {
        if (!this.isAdminMode) return;

        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filteredClients = [];

        // Setup event listeners
        document.getElementById('refreshClientTable').addEventListener('click', () => this.refreshClientTable());
        document.getElementById('deleteSelectedClients').addEventListener('click', () => this.deleteSelectedClients());
        document.getElementById('clientSearchInput').addEventListener('input', (e) => this.filterClients(e.target.value));
        document.getElementById('selectAllClients').addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));
        document.getElementById('prevPage').addEventListener('click', () => this.changePage(-1));
        document.getElementById('nextPage').addEventListener('click', () => this.changePage(1));

        this.refreshClientTable();
    }

    refreshClientTable() {
        const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
        this.allClients = storedClients;
        this.filterClients(document.getElementById('clientSearchInput').value);
    }

    filterClients(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredClients = this.allClients.filter(client =>
            client.clientName.toLowerCase().includes(term) ||
            client.paymentAmount.toString().includes(term) ||
            (client.lastPrize && client.lastPrize.toLowerCase().includes(term))
        );
        this.currentPage = 1;
        this.renderClientTable();
    }

    renderClientTable() {
        const tbody = document.getElementById('clientTableBody');
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageClients = this.filteredClients.slice(startIndex, endIndex);

        tbody.innerHTML = '';

        pageClients.forEach((client, index) => {
            const globalIndex = startIndex + index;
            const tier = client.paymentAmount >= 100 ? 'Premium' : 'Standard';
            const status = client.hasWon ? 'Winner' : client.attempts >= 2 ? 'Completed' : 'Active';
            const statusClass = client.hasWon ? 'status-winner' : client.attempts >= 2 ? 'status-completed' : 'status-active';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="checkbox" class="client-checkbox" data-index="${globalIndex}"></td>
                <td class="client-name">${client.clientName}</td>
                <td class="client-spending">€${client.paymentAmount}</td>
                <td class="client-tier">${tier}</td>
                <td class="client-attempts">${client.attempts}/2</td>
                <td class="client-status ${statusClass}">${status}</td>
                <td class="client-prize">${client.lastPrize || 'None'}</td>
                <td class="client-actions">
                    <button onclick="wheel.editClient(${globalIndex})" class="btn-edit">✏️</button>
                    <button onclick="wheel.deleteClient(${globalIndex})" class="btn-delete">🗑️</button>
                    <button onclick="wheel.resetClient(${globalIndex})" class="btn-reset">🔄</button>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updatePagination();
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredClients.length / this.itemsPerPage);
        document.getElementById('pageInfo').textContent = `Page ${this.currentPage} of ${totalPages}`;
        document.getElementById('prevPage').disabled = this.currentPage <= 1;
        document.getElementById('nextPage').disabled = this.currentPage >= totalPages;
    }

    changePage(direction) {
        const totalPages = Math.ceil(this.filteredClients.length / this.itemsPerPage);
        this.currentPage += direction;
        if (this.currentPage < 1) this.currentPage = 1;
        if (this.currentPage > totalPages) this.currentPage = totalPages;
        this.renderClientTable();
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.client-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = checked);
    }

    editClient(index) {
        const client = this.filteredClients[index];
        if (!client) return;

        const newName = prompt('Edit client name:', client.clientName);
        if (newName === null) return;

        const newAmount = prompt('Edit spending amount (30-200€):', client.paymentAmount);
        if (newAmount === null) return;

        const amount = parseFloat(newAmount);
        if (isNaN(amount) || amount < 30 || amount > 200) {
            alert('Please enter a valid amount between 30€ and 200€');
            return;
        }

        // Update client
        const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
        const clientIndex = storedClients.findIndex(c =>
            c.clientName && c.clientName.toLowerCase() === client.clientName.toLowerCase() && c.paymentAmount === client.paymentAmount
        );

        if (clientIndex >= 0) {
            storedClients[clientIndex].clientName = newName.trim();
            storedClients[clientIndex].paymentAmount = amount;
            localStorage.setItem('sheinClients', JSON.stringify(storedClients));
            this.refreshClientTable();
            this.updateExcelStats();
            alert('Client updated successfully!');
        }
    }

    deleteClient(index) {
        const client = this.filteredClients[index];
        if (!client) return;

        if (!confirm(`Are you sure you want to delete "${client.clientName}"?`)) return;

        const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
        const clientIndex = storedClients.findIndex(c =>
            c.clientName && c.clientName.toLowerCase() === client.clientName.toLowerCase() && c.paymentAmount === client.paymentAmount
        );

        if (clientIndex >= 0) {
            storedClients.splice(clientIndex, 1);
            localStorage.setItem('sheinClients', JSON.stringify(storedClients));
            this.refreshClientTable();
            this.updateExcelStats();
            alert('Client deleted successfully!');
        }
    }

    resetClient(index) {
        const client = this.filteredClients[index];
        if (!client) return;

        if (!confirm(`Reset "${client.clientName}" attempts and prizes?`)) return;

        const storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
        const clientIndex = storedClients.findIndex(c =>
            c.clientName && c.clientName.toLowerCase() === client.clientName.toLowerCase() && c.paymentAmount === client.paymentAmount
        );

        if (clientIndex >= 0) {
            storedClients[clientIndex].attempts = 0;
            storedClients[clientIndex].hasWon = false;
            storedClients[clientIndex].lastPrize = '';
            storedClients[clientIndex].lastWinDate = '';
            storedClients[clientIndex].totalWinnings = 0;
            localStorage.setItem('sheinClients', JSON.stringify(storedClients));
            this.refreshClientTable();
            alert('Client reset successfully!');
        }
    }

    deleteSelectedClients() {
        const checkboxes = document.querySelectorAll('.client-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('Please select clients to delete');
            return;
        }

        if (!confirm(`Delete ${checkboxes.length} selected client(s)?`)) return;

        const indicesToDelete = Array.from(checkboxes).map(cb => parseInt(cb.dataset.index));
        const clientsToDelete = indicesToDelete.map(i => this.filteredClients[i]);

        let storedClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');

        clientsToDelete.forEach(client => {
            const index = storedClients.findIndex(c =>
                c.clientName && c.clientName.toLowerCase() === client.clientName.toLowerCase() && c.paymentAmount === client.paymentAmount
            );
            if (index >= 0) {
                storedClients.splice(index, 1);
            }
        });

        localStorage.setItem('sheinClients', JSON.stringify(storedClients));
        this.refreshClientTable();
        this.updateExcelStats();
        alert(`${checkboxes.length} client(s) deleted successfully!`);
    }

    // Professional Dashboard Setup
    setupProfessionalDashboard() {
        if (!this.isAdminMode) return;

        // Update dashboard overview cards
        this.updateDashboardOverview();

        // Setup dashboard refresh interval
        setInterval(() => {
            if (this.isAdminMode && document.getElementById('adminPanel').style.display !== 'none') {
                this.updateDashboardOverview();
            }
        }, 30000); // Update every 30 seconds
    }

    async updateDashboardOverview() {
        try {
            const result = await window.dbClient.getAllClients();
            const winnersResult = await window.dbClient.getWinners();

            if (result.success) {
                const clients = result.clients || [];
                const winners = winnersResult.success ? winnersResult.winners : [];

                // Calculate statistics
                const totalClients = clients.length;
                const totalWinners = clients.filter(c => c.hasWon).length;
                const totalRevenue = clients.reduce((sum, c) => sum + (c.paymentAmount || 0), 0);
                const totalAttempts = clients.reduce((sum, c) => sum + (c.attempts || 0), 0);
                const winRate = totalAttempts > 0 ? ((totalWinners / totalAttempts) * 100) : 0;

                // Update overview cards safely
                const elements = {
                    totalClientsCount: totalClients,
                    totalWinnersCount: totalWinners,
                    totalRevenueCount: `€${totalRevenue.toFixed(2)}`,
                    winRateCount: `${winRate.toFixed(1)}%`
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });
            } else {
                console.warn('Failed to get clients data:', result.error);
                // Set default values
                const defaultElements = {
                    totalClientsCount: '0',
                    totalWinnersCount: '0',
                    totalRevenueCount: '€0.00',
                    winRateCount: '0%'
                };

                Object.entries(defaultElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });
            }
        } catch (error) {
            console.error('Error updating dashboard overview:', error);
        }
    }

    // Client Management Setup
    setupClientManagement() {
        if (!this.isAdminMode) return;

        // Setup event listeners
        document.getElementById('addClientBtn').addEventListener('click', () => this.showAddClientForm());
        document.getElementById('saveClient').addEventListener('click', () => this.saveNewClient());
        document.getElementById('cancelAddClient').addEventListener('click', () => this.hideAddClientForm());
        document.getElementById('clientSearch').addEventListener('input', (e) => this.searchClients(e.target.value));
        document.getElementById('selectAll').addEventListener('change', (e) => this.selectAllClients(e.target.checked));
        document.getElementById('deleteSelectedBtn').addEventListener('click', () => this.deleteSelectedClients());
        document.getElementById('refreshTableBtn').addEventListener('click', () => this.refreshClientsTable());

        // Load initial client table
        this.refreshClientsTable();
    }

    showAddClientForm() {
        document.getElementById('addClientForm').style.display = 'block';
        document.getElementById('newClientName').focus();
    }

    hideAddClientForm() {
        document.getElementById('addClientForm').style.display = 'none';
        document.getElementById('newClientName').value = '';
        document.getElementById('newClientPayment').value = '';
    }

    async saveNewClient() {
        const nameInput = document.getElementById('newClientName');
        const paymentInput = document.getElementById('newClientPayment');

        if (!nameInput || !paymentInput) {
            console.error('Form inputs not found');
            alert('Form elements not found. Please refresh the page.');
            return;
        }

        const name = nameInput.value.trim();
        const payment = parseFloat(paymentInput.value);

        if (!name) {
            alert('Please enter a client name');
            nameInput.focus();
            return;
        }

        if (!payment || isNaN(payment) || payment < 30 || payment > 200) {
            alert('Please enter a valid payment amount between 30€ and 200€');
            paymentInput.focus();
            return;
        }

        try {
            const result = await window.dbClient.addClient(name, payment, 0, false);

            if (result.success) {
                this.hideAddClientForm();
                this.refreshClientsTable();
                this.updateDashboardOverview();
                this.showSuccessMessage(`Client "${name}" added successfully!`);
            } else {
                alert(`Error: ${result.error}`);
            }
        } catch (error) {
            console.error('Error adding client:', error);
            alert('Error adding client. Please try again.');
        }
    }

    async refreshClientsTable() {
        try {
            const result = await window.dbClient.getAllClients();

            if (result.success) {
                this.displayClientsTable(result.clients);
                document.getElementById('tableInfo').textContent = `Showing ${result.clients.length} clients`;
            }
        } catch (error) {
            console.error('Error refreshing clients table:', error);
        }
    }

    displayClientsTable(clients) {
        const tbody = document.getElementById('clientsTableBody');
        if (!tbody) {
            console.error('Clients table body not found');
            return;
        }

        tbody.innerHTML = '';

        if (!clients || clients.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; padding: 20px; color: #666;">
                        No clients found. Add your first client to get started.
                    </td>
                </tr>
            `;
            return;
        }

        clients.forEach(client => {
            const row = document.createElement('tr');

            // Determine tier
            const tier = client.paymentAmount >= 100 ? 2 : 1;
            const tierClass = tier === 2 ? 'tier-2' : 'tier-1';

            // Determine status
            let status = 'Active';
            let statusClass = 'status-active';

            if (client.hasWon) {
                status = 'Winner';
                statusClass = 'status-winner';
            } else if (client.attempts >= 2) {
                status = 'Exhausted';
                statusClass = 'status-exhausted';
            }

            // Safely handle client name for onclick functions
            const safeClientName = client.clientName.replace(/'/g, "\\'");

            row.innerHTML = `
                <td><input type="checkbox" class="client-checkbox" data-client="${client.clientName}"></td>
                <td><strong>${client.clientName}</strong></td>
                <td>€${(client.paymentAmount || 0).toFixed(2)}</td>
                <td><span class="tier-badge ${tierClass}">Tier ${tier}</span></td>
                <td>${client.attempts || 0}</td>
                <td><span class="status-badge ${statusClass}">${status}</span></td>
                <td>${client.lastPrize || '-'}</td>
                <td>${client.lastWinDate ? new Date(client.lastWinDate).toLocaleDateString() : '-'}</td>
                <td>
                    <button class="action-btn edit" onclick="editClient('${safeClientName}')">✏️</button>
                    <button class="action-btn delete" onclick="deleteClient('${safeClientName}')">🗑️</button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    searchClients(searchTerm) {
        const rows = document.querySelectorAll('#clientsTableBody tr');
        const term = searchTerm.toLowerCase();

        rows.forEach(row => {
            const clientName = row.cells[1].textContent.toLowerCase();
            const visible = clientName.includes(term);
            row.style.display = visible ? '' : 'none';
        });

        // Update table info
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none').length;
        document.getElementById('tableInfo').textContent = `Showing ${visibleRows} clients`;
    }

    selectAllClients(checked) {
        const checkboxes = document.querySelectorAll('.client-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    async deleteSelectedClients() {
        const checkboxes = document.querySelectorAll('.client-checkbox:checked');

        if (checkboxes.length === 0) {
            alert('Please select clients to delete');
            return;
        }

        if (!confirm(`Are you sure you want to delete ${checkboxes.length} client(s)?`)) {
            return;
        }

        let deleted = 0;
        for (const checkbox of checkboxes) {
            try {
                const clientName = checkbox.dataset.client;
                const result = await window.dbClient.deleteClient(clientName);
                if (result.success) {
                    deleted++;
                }
            } catch (error) {
                console.error('Error deleting client:', error);
            }
        }

        this.refreshClientsTable();
        this.updateDashboardOverview();
        alert(`${deleted} client(s) deleted successfully!`);
    }

    // Data Sharing for Multi-Device Access
    setupDataSharing() {
        if (!this.isAdminMode) return;

        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        document.getElementById('importData').addEventListener('click', () => this.importData());
        document.getElementById('dataFileInput').addEventListener('change', (e) => this.handleDataImport(e));
    }

    async exportData() {
        try {
            const result = await window.dbClient.getAllClients();
            const winners = await window.dbClient.getWinners();

            const exportData = {
                clients: result.clients || [],
                winners: winners.winners || [],
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `shein-fortune-wheel-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            this.showSuccessMessage('Data exported successfully! Share this file with other devices.');
        } catch (error) {
            console.error('Error exporting data:', error);
            this.showErrorToUser('Failed to export data');
        }
    }

    importData() {
        document.getElementById('dataFileInput').click();
    }

    async handleDataImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const importData = JSON.parse(text);

            if (!importData.clients || !Array.isArray(importData.clients)) {
                throw new Error('Invalid data format');
            }

            // Import clients
            let imported = 0;
            let updated = 0;

            for (const clientData of importData.clients) {
                const result = await window.dbClient.addClient(
                    clientData.clientName,
                    clientData.paymentAmount,
                    clientData.attempts || 0,
                    clientData.hasWon || false
                );

                if (result.success) {
                    imported++;
                } else if (result.error === 'Client already exists') {
                    // Update existing client
                    await window.dbClient.updateClient(clientData.clientName, {
                        paymentAmount: clientData.paymentAmount,
                        attempts: clientData.attempts || 0,
                        hasWon: clientData.hasWon || false,
                        lastPrize: clientData.lastPrize || '',
                        lastWinDate: clientData.lastWinDate || ''
                    });
                    updated++;
                }
            }

            // Import winners if available
            if (importData.winners && Array.isArray(importData.winners)) {
                for (const winner of importData.winners) {
                    await window.dbClient.addWinner(winner.clientName, winner.prize, winner.timestamp);
                }
            }

            this.refreshClientTable();
            this.updateExcelStats();

            const message = `Import completed!\n• New clients: ${imported}\n• Updated clients: ${updated}`;
            alert(message);

            // Clear file input
            event.target.value = '';

        } catch (error) {
            console.error('Error importing data:', error);
            alert('Error importing data. Please check the file format.');
        }
    }

    // Prize Management Setup
    setupPrizeManagement() {
        if (!this.isAdminMode) return;

        // Setup event listeners
        document.getElementById('savePrizeConfig').addEventListener('click', () => this.savePrizeConfiguration());
        document.getElementById('resetPrizeConfig').addEventListener('click', () => this.resetPrizeConfiguration());

        // Initialize default segments
        this.initializeDefaultSegments();

        // Load saved configuration
        this.loadPrizeConfiguration();
    }

    // Initialize default segments for both tiers
    initializeDefaultSegments() {
        // Default Tier 1 segments (5 segments)
        const tier1Defaults = [
            { name: 'Surprise', chance: 20 },
            { name: '10% Reduction', chance: 20 },
            { name: '5€ Voucher', chance: 20 },
            { name: 'Try Again!', chance: 20 },
            { name: 'Better Luck Next Time', chance: 20 }
        ];

        // Default Tier 2 segments (7 segments)
        const tier2Defaults = [
            { name: 'Surprise', chance: 15 },
            { name: '10% Reduction', chance: 15 },
            { name: '20% Reduction', chance: 15 },
            { name: '5€ Voucher', chance: 15 },
            { name: '10€ Voucher', chance: 15 },
            { name: 'Try Again!', chance: 12.5 },
            { name: 'Better Luck Next Time', chance: 12.5 }
        ];

        this.renderTierSegments('tier1', tier1Defaults);
        this.renderTierSegments('tier2', tier2Defaults);
    }

    // Render segments for a specific tier
    renderTierSegments(tier, segments) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return;

        container.innerHTML = '';

        segments.forEach((segment, index) => {
            const segmentDiv = document.createElement('div');
            segmentDiv.className = 'prize-item';
            segmentDiv.dataset.index = index;

            segmentDiv.innerHTML = `
                <label>Segment ${index + 1}:</label>
                <input type="text" class="segment-name" value="${segment.name}"
                       onchange="updateSegmentTotal('${tier}')" />
                <label>Chance:</label>
                <input type="number" class="segment-chance" value="${segment.chance}"
                       min="0" max="100" step="0.1" onchange="updateSegmentTotal('${tier}')" />%
                <button type="button" class="remove-segment-btn" onclick="removeSpecificSegment('${tier}', ${index})"
                        title="Remove this segment">×</button>
            `;

            container.appendChild(segmentDiv);
        });

        this.updateSegmentCount(tier, segments.length);
        this.updateSegmentTotal(tier);
    }

    // Update segment count display
    updateSegmentCount(tier, count) {
        const countElement = document.getElementById(`${tier}SegmentCount`);
        if (countElement) {
            countElement.textContent = count;
        }
    }

    // Update total percentage display
    updateSegmentTotal(tier) {
        const container = document.getElementById(`${tier}Prizes`);
        const totalElement = document.getElementById(`${tier}Total`);

        if (!container || !totalElement) return;

        const chanceInputs = container.querySelectorAll('.segment-chance');
        let total = 0;

        chanceInputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });

        totalElement.textContent = total.toFixed(1);

        // Update styling based on total
        const totalContainer = totalElement.parentElement;
        totalContainer.classList.remove('valid', 'invalid');

        if (Math.abs(total - 100) < 0.1) {
            totalContainer.classList.add('valid');
        } else {
            totalContainer.classList.add('invalid');
        }
    }

    // Add a new segment to a tier
    addSegmentToTier(tier) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return;

        const currentSegments = container.querySelectorAll('.prize-item').length;
        const newIndex = currentSegments;

        const segmentDiv = document.createElement('div');
        segmentDiv.className = 'prize-item';
        segmentDiv.dataset.index = newIndex;

        segmentDiv.innerHTML = `
            <label>Segment ${newIndex + 1}:</label>
            <input type="text" class="segment-name" value="New Prize"
                   onchange="updateSegmentTotal('${tier}')" />
            <label>Chance:</label>
            <input type="number" class="segment-chance" value="0"
                   min="0" max="100" step="0.1" onchange="updateSegmentTotal('${tier}')" />%
            <button type="button" class="remove-segment-btn" onclick="removeSpecificSegment('${tier}', ${newIndex})"
                    title="Remove this segment">×</button>
        `;

        container.appendChild(segmentDiv);

        this.updateSegmentCount(tier, currentSegments + 1);
        this.updateSegmentTotal(tier);
        this.renumberSegments(tier);
    }

    // Remove the last segment from a tier
    removeLastSegment(tier) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return;

        const segments = container.querySelectorAll('.prize-item');
        if (segments.length <= 2) {
            alert('You must have at least 2 segments per tier');
            return;
        }

        const lastSegment = segments[segments.length - 1];
        lastSegment.remove();

        this.updateSegmentCount(tier, segments.length - 1);
        this.updateSegmentTotal(tier);
        this.renumberSegments(tier);
    }

    // Remove a specific segment
    removeSpecificSegment(tier, index) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return;

        const segments = container.querySelectorAll('.prize-item');
        if (segments.length <= 2) {
            alert('You must have at least 2 segments per tier');
            return;
        }

        const segmentToRemove = container.querySelector(`[data-index="${index}"]`);
        if (segmentToRemove) {
            segmentToRemove.remove();
            this.updateSegmentCount(tier, segments.length - 1);
            this.updateSegmentTotal(tier);
            this.renumberSegments(tier);
        }
    }

    // Renumber segments after addition/removal
    renumberSegments(tier) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return;

        const segments = container.querySelectorAll('.prize-item');
        segments.forEach((segment, index) => {
            segment.dataset.index = index;
            const label = segment.querySelector('label');
            if (label) {
                label.textContent = `Segment ${index + 1}:`;
            }

            // Update remove button onclick
            const removeBtn = segment.querySelector('.remove-segment-btn');
            if (removeBtn) {
                removeBtn.setAttribute('onclick', `removeSpecificSegment('${tier}', ${index})`);
            }
        });
    }

    savePrizeConfiguration() {
        const config = {
            tier1: { prizes: this.getSegmentsFromTier('tier1') },
            tier2: { prizes: this.getSegmentsFromTier('tier2') }
        };

        // Validate total chances = 100%
        const tier1Total = config.tier1.prizes.reduce((sum, p) => sum + p.chance, 0);
        const tier2Total = config.tier2.prizes.reduce((sum, p) => sum + p.chance, 0);

        if (Math.abs(tier1Total - 100) > 0.1) {
            alert(`Error: Tier 1 total chances must equal 100% (currently ${tier1Total.toFixed(1)}%)`);
            return;
        }

        if (Math.abs(tier2Total - 100) > 0.1) {
            alert(`Error: Tier 2 total chances must equal 100% (currently ${tier2Total.toFixed(1)}%)`);
            return;
        }

        localStorage.setItem('sheinPrizeConfig', JSON.stringify(config));
        this.prizeConfig = config;

        // Update wheel configurations
        this.updateWheelConfigurations(config);

        this.showSuccessMessage('Prize configuration saved successfully!');
    }

    // Get segments data from a tier
    getSegmentsFromTier(tier) {
        const container = document.getElementById(`${tier}Prizes`);
        if (!container) return [];

        const segments = [];
        const segmentItems = container.querySelectorAll('.prize-item');

        segmentItems.forEach(item => {
            const nameInput = item.querySelector('.segment-name');
            const chanceInput = item.querySelector('.segment-chance');

            if (nameInput && chanceInput) {
                segments.push({
                    name: nameInput.value.trim(),
                    chance: parseFloat(chanceInput.value) || 0
                });
            }
        });

        return segments;
    }

    // Update wheel configurations based on prize config
    updateWheelConfigurations(config) {
        // Generate colors for segments
        const colors = [
            '#FF6BB7', '#4ECDC4', '#FFB6C1', '#87CEEB', '#DDA0DD',
            '#FFA500', '#FF6347', '#98FB98', '#F0E68C', '#D3D3D3',
            '#FFE4B5', '#E6E6FA', '#FFEFD5', '#F5DEB3', '#FFFACD'
        ];

        // Update tier1 configuration
        this.wheelConfigs.tier1.prizes = config.tier1.prizes.map((prize, index) => ({
            name: prize.name,
            chance: prize.chance,
            color: colors[index % colors.length]
        }));

        // Update tier2 configuration
        this.wheelConfigs.tier2.prizes = config.tier2.prizes.map((prize, index) => ({
            name: prize.name,
            chance: prize.chance,
            color: colors[index % colors.length]
        }));

        console.log('Wheel configurations updated:', this.wheelConfigs);
    }

    resetPrizeConfiguration() {
        if (confirm('Are you sure you want to reset to default prize configuration?')) {
            localStorage.removeItem('sheinPrizeConfig');
            this.initializeDefaultSegments();
            this.showSuccessMessage('Prize configuration reset to default!');
        }
    }

    loadPrizeConfiguration() {
        const saved = localStorage.getItem('sheinPrizeConfig');

        if (saved) {
            try {
                const config = JSON.parse(saved);
                this.prizeConfig = config;

                // Load saved configuration
                if (config.tier1 && config.tier1.prizes) {
                    this.renderTierSegments('tier1', config.tier1.prizes);
                }

                if (config.tier2 && config.tier2.prizes) {
                    this.renderTierSegments('tier2', config.tier2.prizes);
                }

                // Update wheel configurations
                this.updateWheelConfigurations(config);

                console.log('Loaded saved prize configuration');
            } catch (error) {
                console.error('Error loading saved configuration:', error);
                // Fall back to default if saved config is corrupted
                this.initializeDefaultSegments();
            }
        } else {
            // No saved configuration, use defaults
            console.log('No saved configuration found, using defaults');
        }
    }

    // Export Functionality Setup
    setupExportFunctionality() {
        if (!this.isAdminMode) return;

        // Setup event listeners
        document.getElementById('exportJsonBtn').addEventListener('click', () => this.exportAsJSON());
        document.getElementById('exportExcelBtn').addEventListener('click', () => this.exportAsExcel());
        document.getElementById('importDataBtn').addEventListener('click', () => this.importData());
        document.getElementById('dataFileInput').addEventListener('change', (e) => this.handleDataImport(e));
    }

    async exportAsJSON() {
        try {
            const result = await window.dbClient.getAllClients();
            const winnersResult = await window.dbClient.getWinners();

            const exportData = {
                clients: result.success ? result.clients : [],
                winners: winnersResult.success ? winnersResult.winners : [],
                prizeConfig: this.prizeConfig || {},
                exportDate: new Date().toISOString(),
                version: '2.0',
                totalClients: result.success ? result.clients.length : 0
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `shein-fortune-wheel-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showSuccessMessage(`Data exported successfully! ${exportData.totalClients} clients included.`);
        } catch (error) {
            console.error('Error exporting JSON:', error);
            this.showErrorToUser('Failed to export data as JSON. Please try again.');
        }
    }

    async exportAsExcel() {
        try {
            const result = await window.dbClient.getAllClients();

            if (!result.success) {
                alert('Failed to get client data');
                return;
            }

            // Create CSV content (Excel-compatible)
            const headers = ['Client Name', 'Payment Amount (€)', 'Tier', 'Attempts', 'Status', 'Last Prize', 'Win Date', 'Total Winnings'];
            const csvContent = [
                headers.join(','),
                ...result.clients.map(client => {
                    const tier = client.paymentAmount >= 100 ? 'Tier 2' : 'Tier 1';
                    const status = client.hasWon ? 'Winner' : (client.attempts >= 2 ? 'Exhausted' : 'Active');
                    const winDate = client.lastWinDate ? new Date(client.lastWinDate).toLocaleDateString() : '';

                    return [
                        `"${client.clientName}"`,
                        client.paymentAmount.toFixed(2),
                        tier,
                        client.attempts || 0,
                        status,
                        `"${client.lastPrize || ''}"`,
                        winDate,
                        client.totalWinnings || 0
                    ].join(',');
                })
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `shein-clients-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            this.showSuccessMessage('Data exported as Excel/CSV successfully!');
        } catch (error) {
            console.error('Error exporting Excel:', error);
            alert('Failed to export data as Excel');
        }
    }
}

// Collapsible section functionality
function toggleSection(sectionId) {
    const content = document.getElementById(sectionId + 'Content');
    const icon = document.getElementById(sectionId + 'Icon');

    if (content.classList.contains('collapsed')) {
        content.classList.remove('collapsed');
        icon.classList.remove('rotated');
        icon.textContent = '▼';
    } else {
        content.classList.add('collapsed');
        icon.classList.add('rotated');
        icon.textContent = '▶';
    }
}

// Client table toggle functionality
function toggleClientTable() {
    const content = document.getElementById('clientTableContent');
    const toggle = document.getElementById('clientTableToggle');

    if (content.style.maxHeight === '0px' || content.style.maxHeight === '') {
        content.style.maxHeight = '1000px';
        toggle.textContent = '▲';
    } else {
        content.style.maxHeight = '0px';
        toggle.textContent = '▼';
    }
}

// Global functions for segment management
window.addSegment = function(tier) {
    if (window.fortuneWheel) {
        window.fortuneWheel.addSegmentToTier(tier);
    }
};

window.removeSegment = function(tier) {
    if (window.fortuneWheel) {
        window.fortuneWheel.removeLastSegment(tier);
    }
};

window.removeSpecificSegment = function(tier, index) {
    if (window.fortuneWheel) {
        window.fortuneWheel.removeSpecificSegment(tier, index);
    }
};

window.updateSegmentTotal = function(tier) {
    if (window.fortuneWheel) {
        window.fortuneWheel.updateSegmentTotal(tier);
    }
};

// Global functions for table actions
window.editClient = async function(clientName) {
    const newPayment = prompt(`Enter new payment amount for ${clientName}:`, '');
    if (newPayment && !isNaN(newPayment) && newPayment >= 30 && newPayment <= 200) {
        try {
            const result = await window.dbClient.updateClient(clientName, {
                paymentAmount: parseFloat(newPayment)
            });

            if (result.success) {
                window.fortuneWheel.refreshClientsTable();
                window.fortuneWheel.updateDashboardOverview();
                window.fortuneWheel.showSuccessMessage(`Client "${clientName}" updated successfully!`);
            } else {
                alert(`Error: ${result.error}`);
            }
        } catch (error) {
            console.error('Error updating client:', error);
            alert('Error updating client');
        }
    }
};

window.deleteClient = async function(clientName) {
    if (confirm(`Are you sure you want to delete client "${clientName}"?`)) {
        try {
            const result = await window.dbClient.deleteClient(clientName);

            if (result.success) {
                window.fortuneWheel.refreshClientsTable();
                window.fortuneWheel.updateDashboardOverview();
                window.fortuneWheel.showSuccessMessage(`Client "${clientName}" deleted successfully!`);
            } else {
                alert(`Error: ${result.error}`);
            }
        } catch (error) {
            console.error('Error deleting client:', error);
            alert('Error deleting client');
        }
    }
};

// Initialize the wheel when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.fortuneWheel = new FortuneWheel();
});

// Initialize the wheel when page loads
let wheel;
document.addEventListener('DOMContentLoaded', () => {
    wheel = new FortuneWheel();
});
