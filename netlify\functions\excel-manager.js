// Netlify Function to manage client data (JSON-based storage)
const fs = require('fs').promises;
const path = require('path');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const dataDir = '/tmp/shein-data';
  const clientsFilePath = path.join(dataDir, 'clients.json');

  try {
    // Ensure data directory exists
    await fs.mkdir(dataDir, { recursive: true });

    if (event.httpMethod === 'GET') {
      // Get client data or export Excel file
      const queryParams = event.queryStringParameters || {};
      const action = queryParams.action;
      const clientName = queryParams.clientName;

      if (action === 'export') {
        // Export client data as CSV
        try {
          const clientsData = await getClientsFromJSON(clientsFilePath);
          const csvContent = convertToCSV(clientsData);
          return {
            statusCode: 200,
            headers: {
              ...headers,
              'Content-Type': 'text/csv',
              'Content-Disposition': 'attachment; filename="winnings.csv"'
            },
            body: csvContent
          };
        } catch (err) {
          return {
            statusCode: 404,
            headers,
            body: JSON.stringify({ error: 'Client data not found' })
          };
        }
      }

      if (action === 'getClient' && clientName) {
        // Get specific client data
        const clientData = await getClientFromJSON(clientsFilePath, clientName);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(clientData)
        };
      }

      // Get all clients
      const allClients = await getClientsFromJSON(clientsFilePath);
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ clients: allClients })
      };
    }

    if (event.httpMethod === 'POST') {
      // Add new client or update existing
      const { clientName, paymentAmount, action } = JSON.parse(event.body);

      if (action === 'addClient') {
        const result = await addClientToJSON(clientsFilePath, clientName, paymentAmount);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result)
        };
      }

      if (action === 'updateWinning') {
        const { prize, timestamp } = JSON.parse(event.body);
        const result = await updateClientWinning(clientsFilePath, clientName, prize, timestamp);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result)
        };
      }
    }

    if (event.httpMethod === 'PUT') {
      // Upload client data (JSON format)
      const { clientsData } = JSON.parse(event.body);
      await fs.writeFile(clientsFilePath, JSON.stringify(clientsData, null, 2));

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ success: true, message: 'Client data uploaded successfully' })
      };
    }

    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };

  } catch (error) {
    console.error('Excel manager error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        details: error.message
      })
    };
  }
};

async function getClientFromJSON(filePath, clientName) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    const clients = JSON.parse(data);

    const client = clients.find(c =>
      c.clientName && c.clientName.toLowerCase() === clientName.toLowerCase()
    );

    if (client) {
      return {
        found: true,
        clientName: client.clientName,
        paymentAmount: client.paymentAmount || 0,
        attempts: client.attempts || 0,
        lastPrize: client.lastPrize || null,
        lastWinDate: client.lastWinDate || null,
        totalWinnings: client.totalWinnings || 0
      };
    }

    return { found: false };
  } catch (error) {
    // File doesn't exist, create empty array
    await fs.writeFile(filePath, JSON.stringify([], null, 2));
    return { found: false };
  }
}

async function getClientsFromJSON(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    await fs.writeFile(filePath, JSON.stringify([], null, 2));
    return [];
  }
}

async function addClientToJSON(filePath, clientName, paymentAmount) {
  try {
    let clients = [];

    try {
      const data = await fs.readFile(filePath, 'utf8');
      clients = JSON.parse(data);
    } catch (error) {
      // File doesn't exist, start with empty array
      clients = [];
    }

    // Check if client already exists
    const existingClientIndex = clients.findIndex(c =>
      c.clientName && c.clientName.toLowerCase() === clientName.toLowerCase()
    );

    if (existingClientIndex >= 0) {
      // Update existing client
      clients[existingClientIndex].paymentAmount = paymentAmount;
      clients[existingClientIndex].lastUpdated = new Date().toLocaleString();
    } else {
      // Add new client
      clients.push({
        clientName: clientName,
        paymentAmount: paymentAmount,
        attempts: 0,
        lastPrize: '',
        lastWinDate: '',
        totalWinnings: 0,
        dateAdded: new Date().toLocaleString(),
        lastUpdated: new Date().toLocaleString()
      });
    }

    // Write to file
    await fs.writeFile(filePath, JSON.stringify(clients, null, 2));

    return {
      success: true,
      message: existingClientIndex >= 0 ? 'Client updated' : 'Client added',
      clientData: clients[existingClientIndex >= 0 ? existingClientIndex : clients.length - 1]
    };
  } catch (error) {
    throw new Error(`Failed to add client: ${error.message}`);
  }
}

async function updateClientWinning(filePath, clientName, prize, timestamp) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    const clients = JSON.parse(data);

    const clientIndex = clients.findIndex(c =>
      c.clientName && c.clientName.toLowerCase() === clientName.toLowerCase()
    );

    if (clientIndex >= 0) {
      clients[clientIndex].attempts = (clients[clientIndex].attempts || 0) + 1;
      clients[clientIndex].lastPrize = prize;
      clients[clientIndex].lastWinDate = timestamp;

      // Only increment total winnings if it's not a losing prize
      if (!prize.includes('Try Again') && !prize.includes('Better Luck')) {
        clients[clientIndex].totalWinnings = (clients[clientIndex].totalWinnings || 0) + 1;
      }

      clients[clientIndex].lastUpdated = new Date().toLocaleString();

      // Write to file
      await fs.writeFile(filePath, JSON.stringify(clients, null, 2));

      return {
        success: true,
        message: 'Client winning updated',
        clientData: clients[clientIndex]
      };
    }

    return { success: false, message: 'Client not found' };
  } catch (error) {
    throw new Error(`Failed to update client winning: ${error.message}`);
  }
}

function convertToCSV(clients) {
  if (!clients || clients.length === 0) {
    return 'Client Name,Payment Amount,Attempts,Last Prize,Last Win Date,Total Winnings,Date Added,Last Updated\n';
  }

  const headers = 'Client Name,Payment Amount,Attempts,Last Prize,Last Win Date,Total Winnings,Date Added,Last Updated\n';
  const rows = clients.map(client => {
    return [
      client.clientName || '',
      client.paymentAmount || '',
      client.attempts || 0,
      client.lastPrize || '',
      client.lastWinDate || '',
      client.totalWinnings || 0,
      client.dateAdded || '',
      client.lastUpdated || ''
    ].map(field => `"${field}"`).join(',');
  }).join('\n');

  return headers + rows;
}
