# Shein Fortune Wheel - Netlify Deployment Guide

## 🚀 Quick Deployment to Netlify

### Method 1: Drag & Drop (Easiest)
1. Zip all files in this directory
2. Go to [Netlify](https://netlify.com)
3. Drag and drop the zip file to deploy
4. Your site will be live instantly!

### Method 2: Git Integration (Recommended)
1. Push this code to a GitHub repository
2. Connect your GitHub account to Netlify
3. Select your repository
4. Deploy automatically

### Method 3: Netlify CLI
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy from this directory
netlify deploy --prod
```

## 📁 Project Structure
```
shein-wheel/
├── register.html          # Registration page (entry point)
├── wheel.html             # Main wheel page
├── index.html             # Redirects to register.html
├── register-styles.css    # Registration page styles
├── wheel-styles.css       # Wheel page styles
├── register-script.js     # Registration functionality
├── wheel-script.js        # Wheel functionality
├── netlify.toml          # Netlify configuration
├── package.json          # Project metadata
├── netlify/functions/    # Serverless functions
│   ├── save-winner.js    # Save winner data
│   ├── get-winners.js    # Retrieve winner data
│   └── export-data.js    # Export data functionality
└── DEPLOYMENT.md         # This file
```

## ⚙️ Configuration

### Environment Variables (Optional)
You can set these in Netlify dashboard under Site Settings > Environment Variables:

- `ADMIN_PASSWORD`: Custom admin password (default: "youtoshein-03")
- `DATA_RETENTION_DAYS`: How long to keep data (default: 365)

### Custom Domain
1. Go to Netlify dashboard
2. Site Settings > Domain Management
3. Add your custom domain
4. Configure DNS settings

## 🔧 Features

### Client-Side Features
- ✅ Equal-sized wheel segments with weighted winning chances
- ✅ Beautiful registration page
- ✅ Popup congratulations with confetti
- ✅ Local data persistence
- ✅ Responsive design

### Server-Side Features
- ✅ Persistent data storage via Netlify Functions
- ✅ Automatic data backup
- ✅ Export functionality (TXT, CSV, JSON)
- ✅ Client information tracking
- ✅ Admin panel with full control

### Data Storage
- **Local Storage**: For immediate access and offline functionality
- **Server Storage**: For persistence and admin access
- **Export Options**: Multiple formats for data analysis

## 📊 Data Management

### Automatic Data Collection
The application automatically saves:
- Winner information (name, prize, timestamp)
- Client session data
- User agent information
- Session IDs for tracking

### Export Options
Admins can export data in multiple formats:
- **TXT**: Simple text format for easy reading
- **CSV**: Spreadsheet-compatible format
- **JSON**: Structured data for analysis

### API Endpoints
- `/.netlify/functions/save-winner` - Save winner data
- `/.netlify/functions/get-winners` - Retrieve winner data
- `/.netlify/functions/export-data` - Export data in various formats

## 🔐 Security Features

### Admin Access
- Password-protected admin panel
- Session-based authentication
- Secure data handling

### Data Protection
- CORS headers configured
- Input validation
- Error handling
- No sensitive data exposure

## 🎯 Usage Instructions

### For Clients
1. Visit the deployed site
2. Enter your name on the registration page
3. Click "START SPINNING"
4. Spin the wheel and enjoy your prize!

### For Admins
1. Click "Admin Access" on registration page
2. Enter password: `youtoshein-03`
3. Manage Excel file with client payment data
4. Add clients manually or upload Excel file
5. Access full prize management
6. View winner history and export data
7. Monitor client attempts and winnings

## 🛠️ Customization

### Changing Colors
Edit the CSS variables in `wheel-styles.css`:
```css
:root {
  --primary-color: #FF6BB7;
  --secondary-color: #4ECDC4;
}
```

### Adding New Prizes
Use the admin panel or modify the default prizes in `wheel-script.js`:
```javascript
this.prizes = [
    { name: 'Your Prize', chance: 20, color: '#FF6BB7' }
];
```

### Modifying Admin Password
Change in `register-script.js`:
```javascript
if (password === 'your-new-password') {
```

## 📱 Mobile Optimization

The application is fully responsive and optimized for:
- Mobile phones (iOS/Android)
- Tablets
- Desktop computers
- Touch and mouse interactions

## 🔄 Updates and Maintenance

### Updating the Application
1. Make changes to your code
2. Push to GitHub (if using Git integration)
3. Netlify will automatically redeploy

### Monitoring
- Check Netlify dashboard for deployment status
- Monitor function logs for any issues
- Review data exports regularly

## 🆘 Troubleshooting

### Common Issues
1. **Functions not working**: Check Netlify Functions tab in dashboard
2. **Data not saving**: Verify function logs for errors
3. **Site not loading**: Check build logs in Netlify dashboard

### Support
- Check Netlify documentation
- Review function logs
- Test locally with `netlify dev`

## 📈 Analytics

Netlify provides built-in analytics:
- Page views
- Unique visitors
- Function invocations
- Performance metrics

Access these in your Netlify dashboard under Analytics.

---

**🎉 Your Shein Fortune Wheel is ready for deployment!**

Simply follow the deployment steps above and your interactive prize wheel will be live on the internet with full data persistence and admin capabilities.
