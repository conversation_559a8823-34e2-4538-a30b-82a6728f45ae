// Netlify Function to save winner data
const fs = require('fs').promises;
const path = require('path');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // <PERSON>le preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const { clientName, prize, timestamp, clientInfo } = JSON.parse(event.body);

    if (!clientName || !prize || !timestamp) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required fields' })
      };
    }

    // Create data directory if it doesn't exist
    const dataDir = '/tmp/shein-data';
    try {
      await fs.mkdir(dataDir, { recursive: true });
    } catch (err) {
      // Directory might already exist
    }

    // Prepare winner data
    const winnerData = {
      clientName,
      prize,
      timestamp,
      clientInfo: clientInfo || {},
      id: Date.now().toString()
    };

    // Save to JSON file for structured data
    const jsonFilePath = path.join(dataDir, 'winners.json');
    let existingData = [];
    
    try {
      const existingContent = await fs.readFile(jsonFilePath, 'utf8');
      existingData = JSON.parse(existingContent);
    } catch (err) {
      // File doesn't exist yet, start with empty array
    }

    existingData.push(winnerData);
    await fs.writeFile(jsonFilePath, JSON.stringify(existingData, null, 2));

    // Also save to text file for easy reading
    const txtFilePath = path.join(dataDir, 'winners.txt');
    const textEntry = `${timestamp} - ${clientName}: ${prize}\n`;
    
    try {
      await fs.appendFile(txtFilePath, textEntry);
    } catch (err) {
      await fs.writeFile(txtFilePath, textEntry);
    }

    // Save detailed client information if provided
    if (clientInfo && Object.keys(clientInfo).length > 0) {
      const clientsFilePath = path.join(dataDir, 'clients.json');
      let clientsData = [];
      
      try {
        const existingClients = await fs.readFile(clientsFilePath, 'utf8');
        clientsData = JSON.parse(existingClients);
      } catch (err) {
        // File doesn't exist yet
      }

      // Check if client already exists, update or add
      const existingClientIndex = clientsData.findIndex(c => c.name === clientName);
      if (existingClientIndex >= 0) {
        clientsData[existingClientIndex] = {
          ...clientsData[existingClientIndex],
          ...clientInfo,
          lastWin: { prize, timestamp },
          totalWins: (clientsData[existingClientIndex].totalWins || 0) + 1
        };
      } else {
        clientsData.push({
          name: clientName,
          ...clientInfo,
          firstWin: { prize, timestamp },
          lastWin: { prize, timestamp },
          totalWins: 1
        });
      }

      await fs.writeFile(clientsFilePath, JSON.stringify(clientsData, null, 2));
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ 
        success: true, 
        message: 'Winner data saved successfully',
        id: winnerData.id
      })
    };

  } catch (error) {
    console.error('Error saving winner data:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Failed to save winner data',
        details: error.message
      })
    };
  }
};
