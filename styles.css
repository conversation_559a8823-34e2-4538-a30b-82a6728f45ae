/* Shein <PERSON> Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Wheel Section */
.wheel-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.wheel-container {
    position: relative;
    margin-bottom: 30px;
}

#wheelCanvas {
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    transition: transform 0.1s ease;
    width: 600px;
    height: 600px;
}

.center-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    z-index: 10;
}

#centerImage {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.pointer {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 30px solid #FF6BB7;
    z-index: 15;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.spin-button {
    background: linear-gradient(45deg, #FF6BB7, #FF8CC8);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 183, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.spin-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 183, 0.6);
}

.spin-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.result-display {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Controls Section */
.controls-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.client-form, .prize-management, .wheel-controls, .winners-history {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.client-form h3, .prize-management h3, .wheel-controls h3, .winners-history h3 {
    color: #FF6BB7;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* Form Elements */
input[type="text"], input[type="number"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 10px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: #FF6BB7;
}

input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

input[type="range"] {
    width: 100%;
    margin: 10px 0;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-danger {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
}

.btn-primary {
    background: #FF6BB7;
    color: white;
}

.btn-secondary {
    background: #4ECDC4;
    color: white;
}

.btn-danger {
    background: #ff4757;
    color: white;
}

.btn-primary:hover, .btn-secondary:hover, .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Prize Management */
.add-prize-form {
    display: grid;
    grid-template-columns: 1fr 80px 60px 100px;
    gap: 10px;
    align-items: end;
    margin-bottom: 20px;
}

.prize-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.prize-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
}

.prize-info {
    flex: 1;
}

.prize-actions button {
    padding: 5px 10px;
    font-size: 0.8rem;
    margin-left: 5px;
}

/* History */
.history-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #FF6BB7;
}

.history-item .timestamp {
    font-size: 0.8rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .add-prize-form {
        grid-template-columns: 1fr;
    }
    
    #wheelCanvas {
        width: 300px;
        height: 300px;
    }
}

/* Spinning Animation */
.spinning {
    animation: spin linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Collapsible Sections */
.collapsible-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.collapsible-header:hover {
    color: #FF8CC8;
}

.toggle-icon {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.toggle-icon.rotated {
    transform: rotate(-90deg);
}

.collapsible-content {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible-content.collapsed {
    max-height: 0;
}

/* Winner Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.5s ease;
    overflow: hidden;
}

.modal-header {
    text-align: center;
    padding: 30px 20px 20px;
    color: white;
}

.modal-header h2 {
    font-size: 2.5rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: bounce 1s ease infinite alternate;
}

.modal-body {
    padding: 20px;
    text-align: center;
    position: relative;
    background: white;
    margin: 0 20px;
    border-radius: 15px;
    box-shadow: inset 0 4px 15px rgba(0, 0, 0, 0.1);
}

.winner-info {
    padding: 20px 0;
}

.winner-name {
    font-size: 1.8rem;
    font-weight: bold;
    color: #FF6BB7;
    margin-bottom: 15px;
}

.winner-prize {
    font-size: 2.2rem;
    font-weight: bold;
    color: #4ECDC4;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.celebration-text {
    font-size: 1.2rem;
    color: #666;
    font-style: italic;
}

.modal-footer {
    padding: 20px;
    text-align: center;
}

.modal-btn {
    font-size: 1.3rem;
    padding: 15px 40px;
    border-radius: 50px;
    background: linear-gradient(45deg, #FF6BB7, #FF8CC8);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 183, 0.4);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 183, 0.6);
}

/* Confetti Animation */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #FF6BB7;
    animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    background: #FF6BB7;
}

.confetti:nth-child(2) {
    left: 30%;
    animation-delay: 0.5s;
    background: #4ECDC4;
}

.confetti:nth-child(3) {
    left: 50%;
    animation-delay: 1s;
    background: #FFB6C1;
}

.confetti:nth-child(4) {
    left: 70%;
    animation-delay: 1.5s;
    background: #87CEEB;
}

.confetti:nth-child(5) {
    left: 90%;
    animation-delay: 2s;
    background: #DDA0DD;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    from { transform: translateY(0px); }
    to { transform: translateY(-10px); }
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(400px) rotate(720deg);
        opacity: 0;
    }
}
