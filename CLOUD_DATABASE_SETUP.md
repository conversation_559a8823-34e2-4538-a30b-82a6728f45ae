# Cloud Database Setup Guide

This guide explains how to set up a real cloud database for the Shein Fortune Wheel to share data across all devices and networks.

## Current Status

The application currently uses **localStorage** which means:
- ✅ **Works immediately** - no setup required
- ❌ **Device-specific** - data doesn't sync between devices
- ✅ **Export/Import feature** - manual data sharing between devices

## Option 1: JSONBin.io (Recommended - Free)

### Step 1: Create a JSONBin Account
1. Go to [jsonbin.io](https://jsonbin.io)
2. Sign up for a free account
3. Create a new bin with this initial data:

```json
{
  "clients": [],
  "winners": [],
  "settings": {
    "created": "2024-08-24T10:00:00.000Z",
    "version": "1.0"
  }
}
```

### Step 2: Get Your Credentials
1. Copy your **Bin ID** (e.g., `67419b8bad19ca34f8c8f8c8`)
2. Copy your **API Key** (from your account settings)

### Step 3: Update the Code
In `database-client.js`, replace these lines:

```javascript
// Change this:
this.useCloudDB = false;

// To this:
this.useCloudDB = true;
this.jsonBinUrl = 'https://api.jsonbin.io/v3/b';
this.binId = '68ab525dd0ea881f4063078c';
this.apiKey = '$2a$10$Twch63QhrK5EKGmGCrECfOxy0whAiFxFGWcDyOdWoKpuO0cpaIBge';
```

### Step 4: Update API Methods
Replace the `apiCall` method with:

```javascript
async apiCall(action, data = {}) {
    if (this.useCloudDB) {
        try {
            return await this.handleJSONBin(action, data);
        } catch (error) {
            console.error('Cloud database failed:', error);
            return this.handleLocalFallback(action, data);
        }
    } else {
        return this.handleLocalFallback(action, data);
    }
}
```

## Option 2: Firebase Realtime Database (Google)

### Step 1: Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Realtime Database
4. Set rules to public (for testing):

```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```

### Step 2: Get Database URL
Copy your database URL (e.g., `https://your-project.firebaseio.com/`)

### Step 3: Update Code
Replace the database client with Firebase SDK calls.

## Option 3: Supabase (PostgreSQL)

### Step 1: Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Create tables for clients and winners

### Step 2: Get API Keys
Copy your project URL and anon key

### Step 3: Update Code
Use Supabase JavaScript client for database operations.

## Option 4: Simple GitHub Gist (Read-Only)

### Step 1: Create Public Gist
1. Go to [gist.github.com](https://gist.github.com)
2. Create a public gist named `shein-database.json`
3. Add initial data structure

### Step 2: Get Gist ID
Copy the gist ID from the URL

### Step 3: Update Code
Use GitHub API to read data (write operations require authentication)

## Testing Your Setup

1. **Add a test client** on one device
2. **Check on another device** - client should appear
3. **Play the wheel** - results should sync
4. **Verify in database** - data should be stored in cloud

## Troubleshooting

### Connection Issues
- Check your API keys and URLs
- Verify CORS settings
- Test with browser developer tools

### Data Not Syncing
- Confirm write permissions
- Check for API rate limits
- Verify data format matches expected structure

### Fallback Mode
- App automatically falls back to localStorage if cloud fails
- Use Export/Import feature as backup

## Security Considerations

### For Production Use:
1. **Restrict API access** - limit to your domain
2. **Use environment variables** - don't hardcode keys
3. **Implement authentication** - protect sensitive data
4. **Set up proper CORS** - restrict origins
5. **Monitor usage** - watch for abuse

### Database Rules Example (Firebase):
```json
{
  "rules": {
    "clients": {
      ".read": true,
      ".write": "auth != null"
    },
    "winners": {
      ".read": true,
      ".write": "auth != null"
    }
  }
}
```

## Current Workaround

Until you set up a cloud database, use the **Export/Import feature**:

1. **Device A**: Add clients → Export data
2. **Device B**: Import data → All clients available
3. **Sync regularly** by exporting from the device with latest data

This provides cross-device functionality without requiring cloud setup.

## Support

If you need help setting up a cloud database:
1. Choose your preferred option above
2. Follow the step-by-step guide
3. Test thoroughly before production use
4. Keep the Export/Import feature as backup

The localStorage version works perfectly for single-device use and provides a reliable fallback for all scenarios.
