/* Shein Registration Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.registration-card {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 500px;
    width: 90%;
    position: relative;
    z-index: 10;
    animation: slideInUp 0.8s ease;
}

.header {
    margin-bottom: 40px;
}

.logo {
    margin-bottom: 20px;
}

.logo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.header h1 {
    font-size: 2.5rem;
    color: #FF6BB7;
    margin-bottom: 10px;
    font-weight: bold;
}

.header p {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.5;
}

.form-section {
    margin-bottom: 40px;
}

.input-group {
    margin-bottom: 30px;
    text-align: left;
}

.input-group label {
    display: block;
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.input-group input {
    width: 100%;
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 3px solid #e0e0e0;
    border-radius: 15px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: #FF6BB7;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 107, 183, 0.1);
}

.input-error {
    color: #ff4757;
    font-size: 0.9rem;
    margin-top: 5px;
    min-height: 20px;
}

.start-button {
    background: linear-gradient(45deg, #FF6BB7, #FF8CC8);
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.3rem;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 183, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
}

.start-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 183, 0.6);
}

.start-button:active {
    transform: translateY(-1px);
}

.features {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    padding: 20px 0;
    border-top: 2px solid #f0f0f0;
}

.feature {
    text-align: center;
    flex: 1;
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.feature-text {
    font-size: 0.9rem;
    color: #666;
    font-weight: bold;
}

.admin-section {
    border-top: 2px solid #f0f0f0;
    padding-top: 20px;
}

.admin-button {
    background: transparent;
    color: #4ECDC4;
    border: 2px solid #4ECDC4;
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.admin-button:hover {
    background: #4ECDC4;
    color: white;
    transform: translateY(-2px);
}

/* Background Decorations */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape1 {
    width: 100px;
    height: 100px;
    background: white;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape2 {
    width: 150px;
    height: 150px;
    background: white;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.shape3 {
    width: 80px;
    height: 80px;
    background: white;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape4 {
    width: 120px;
    height: 120px;
    background: white;
    bottom: 15%;
    right: 10%;
    animation-delay: 1s;
}

/* Animations */
@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .registration-card {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .header p {
        font-size: 1rem;
    }
    
    .features {
        flex-direction: column;
        gap: 15px;
    }
    
    .floating-shape {
        display: none;
    }
}
