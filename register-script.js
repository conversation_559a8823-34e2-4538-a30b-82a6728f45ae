// Shein Registration Page JavaScript

// Global error handling for registration page
window.addEventListener('error', (event) => {
    console.error('Registration page error:', event.error);
    showErrorNotification('Something went wrong. Please refresh the page.');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showErrorNotification('An unexpected error occurred.');
    event.preventDefault();
});

function showErrorNotification(message) {
    let notification = document.getElementById('errorNotification');
    if (!notification) {
        notification = document.createElement('div');
        notification.id = 'errorNotification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        document.body.appendChild(notification);
    }

    notification.innerHTML = `
        <div style="display: flex; align-items: center;">
            <span style="margin-right: 10px;">⚠️</span>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.style.display='none'"
                    style="background: none; border: none; color: white; margin-left: 10px; cursor: pointer;">×</button>
        </div>
    `;

    notification.style.display = 'block';

    setTimeout(() => {
        if (notification) {
            notification.style.display = 'none';
        }
    }, 5000);
}

class Registration {
    constructor() {
        try {
            this.init();
        } catch (error) {
            console.error('Failed to initialize registration:', error);
            showErrorNotification('Failed to initialize registration page.');
        }
    }
    
    init() {
        this.setupEventListeners();
        this.checkExistingSession();
    }
    
    setupEventListeners() {
        const startButton = document.getElementById('startSpinning');
        const nameInput = document.getElementById('clientName');
        const adminButton = document.getElementById('adminAccess');
        
        startButton.addEventListener('click', () => this.handleRegistration());
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleRegistration();
            }
        });
        nameInput.addEventListener('input', () => this.clearError());
        adminButton.addEventListener('click', () => this.handleAdminAccess());
        
        // Focus on name input when page loads
        nameInput.focus();
    }
    
    handleRegistration() {
        try {
            const nameInput = document.getElementById('clientName');
            if (!nameInput) {
                throw new Error('Name input element not found');
            }

            const name = nameInput.value.trim();

            if (!this.validateName(name)) {
                return;
            }

            // Save client name to sessionStorage
            sessionStorage.setItem('sheinClientName', name);

            // Add loading state to button
            this.setLoadingState(true);

            // Check if wheel page exists before redirecting
            this.checkPageExists('wheel.html').then(exists => {
                if (exists) {
                    // Redirect to wheel page after short delay for better UX
                    setTimeout(() => {
                        window.location.href = 'wheel.html';
                    }, 500);
                } else {
                    this.setLoadingState(false);
                    this.showError('Wheel page not found. Please contact support.');
                }
            }).catch(error => {
                console.error('Error checking wheel page:', error);
                // Proceed with redirect anyway
                setTimeout(() => {
                    window.location.href = 'wheel.html';
                }, 500);
            });

        } catch (error) {
            console.error('Registration error:', error);
            this.setLoadingState(false);
            showErrorNotification('Registration failed. Please try again.');
        }
    }

    async checkPageExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.warn('Could not verify page exists:', url);
            return true; // Assume it exists if we can't check
        }
    }
    
    validateName(name) {
        const errorElement = document.getElementById('nameError');
        
        if (!name) {
            this.showError('Please enter your name');
            return false;
        }
        
        if (name.length < 2) {
            this.showError('Name must be at least 2 characters long');
            return false;
        }
        
        if (name.length > 50) {
            this.showError('Name must be less than 50 characters');
            return false;
        }
        
        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        const nameRegex = /^[a-zA-Z\s\-']+$/;
        if (!nameRegex.test(name)) {
            this.showError('Name can only contain letters, spaces, hyphens, and apostrophes');
            return false;
        }
        
        return true;
    }
    
    showError(message) {
        const errorElement = document.getElementById('nameError');
        const nameInput = document.getElementById('clientName');
        
        errorElement.textContent = message;
        nameInput.style.borderColor = '#ff4757';
        nameInput.focus();
    }
    
    clearError() {
        const errorElement = document.getElementById('nameError');
        const nameInput = document.getElementById('clientName');
        
        errorElement.textContent = '';
        nameInput.style.borderColor = '#e0e0e0';
    }
    
    setLoadingState(loading) {
        const button = document.getElementById('startSpinning');
        const buttonText = button.querySelector('.button-text');
        const buttonIcon = button.querySelector('.button-icon');
        
        if (loading) {
            button.disabled = true;
            button.style.opacity = '0.8';
            buttonText.textContent = 'LOADING...';
            buttonIcon.textContent = '⏳';
        } else {
            button.disabled = false;
            button.style.opacity = '1';
            buttonText.textContent = 'START SPINNING';
            buttonIcon.textContent = '🎯';
        }
    }
    
    checkExistingSession() {
        const existingName = sessionStorage.getItem('sheinClientName');
        if (existingName) {
            document.getElementById('clientName').value = existingName;
        }
    }
    
    handleAdminAccess() {
        const password = prompt('Enter admin password:');
        if (password === 'youtoshein-03') {
            sessionStorage.setItem('sheinAdminMode', 'true');
            window.location.href = 'wheel.html';
        } else if (password !== null) {
            alert('Incorrect password');
        }
    }
}

// Add some visual enhancements
document.addEventListener('DOMContentLoaded', () => {
    new Registration();
    
    // Add particle effect on button hover
    const startButton = document.getElementById('startSpinning');
    startButton.addEventListener('mouseenter', createParticles);
});

function createParticles() {
    const button = document.getElementById('startSpinning');
    const rect = button.getBoundingClientRect();
    
    for (let i = 0; i < 5; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.background = '#FF6BB7';
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '1000';
        particle.style.left = (rect.left + Math.random() * rect.width) + 'px';
        particle.style.top = (rect.top + Math.random() * rect.height) + 'px';
        
        document.body.appendChild(particle);
        
        // Animate particle
        particle.animate([
            { transform: 'translateY(0px)', opacity: 1 },
            { transform: 'translateY(-30px)', opacity: 0 }
        ], {
            duration: 1000,
            easing: 'ease-out'
        }).onfinish = () => {
            particle.remove();
        };
    }
}
