<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shein Fortune Wheel - Registration</title>
    <link rel="icon" type="image/png" href="shein-logo.png">
    <link rel="shortcut icon" type="image/png" href="shein-logo.png">
    <link rel="stylesheet" href="register-styles.css">
    <!-- Database Client -->
    <script src="database-client.js"></script>
</head>
<body>
    <div class="container">
        <div class="registration-card">
            <div class="header">
                <div class="logo">
                    <img src="shein-logo.png" alt="Shein Logo">
                </div>
                <h1>Welcome to Shein Fortune Wheel</h1>
                <p>Enter your name to start spinning and win amazing prizes!</p>
            </div>
            
            <div class="form-section">
                <div class="input-group">
                    <label for="clientName">Your Name</label>
                    <input type="text" id="clientName" placeholder="Enter your full name" required>
                    <div class="input-error" id="nameError"></div>
                </div>
                
                <button id="startSpinning" class="start-button">
                    <span class="button-text">START SPINNING</span>
                    <span class="button-icon">🎯</span>
                </button>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🎁</div>
                    <div class="feature-text">Amazing Prizes</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">💫</div>
                    <div class="feature-text">Instant Results</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎉</div>
                    <div class="feature-text">Fun Experience</div>
                </div>
            </div>
            
            <div class="admin-section">
                <button id="adminAccess" class="admin-button">Admin Access</button>
            </div>
        </div>
        
        <div class="background-decoration">
            <div class="floating-shape shape1"></div>
            <div class="floating-shape shape2"></div>
            <div class="floating-shape shape3"></div>
            <div class="floating-shape shape4"></div>
        </div>
    </div>

    <script src="register-script.js"></script>
</body>
</html>
