<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Shein Fortune Wheel</title>
    <link rel="icon" type="image/png" href="shein-logo.png">
    <link rel="shortcut icon" type="image/png" href="shein-logo.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FF6BB7 0%, #4ECDC4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            max-width: 500px;
            margin: 20px;
            animation: fadeIn 0.6s ease-out;
        }
        
        .error-icon {
            font-size: 5rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #FF6BB7;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #FF6BB7, #FF8CC8);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #4ECDC4, #45B7B8);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .suggestions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
            text-align: left;
        }
        
        .suggestions h3 {
            color: #FF6BB7;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .suggestions ul {
            list-style: none;
            padding: 0;
        }
        
        .suggestions li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .suggestions li:last-child {
            border-bottom: none;
        }
        
        .suggestions a {
            color: #4ECDC4;
            text-decoration: none;
            font-weight: 500;
        }
        
        .suggestions a:hover {
            color: #FF6BB7;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @media (max-width: 480px) {
            .error-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .error-code {
                font-size: 4rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🎯</div>
        <div class="error-code">404</div>
        <h1>Page Not Found</h1>
        <p>Oops! The page you're looking for seems to have spun away like our fortune wheel!</p>
        <p>Don't worry, let's get you back to the fun.</p>
        
        <div class="buttons">
            <a href="/register.html" class="btn btn-primary">🎮 Start Playing</a>
            <a href="/wheel.html" class="btn btn-secondary">🎡 Go to Wheel</a>
        </div>
        
        <div class="suggestions">
            <h3>🔍 Quick Links</h3>
            <ul>
                <li><a href="/register.html">🏠 Registration Page</a></li>
                <li><a href="/wheel.html">🎡 Fortune Wheel</a></li>
                <li><a href="/index.html">📋 Main Page</a></li>
            </ul>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-redirect after 10 seconds if no interaction
            let countdown = 10;
            const autoRedirect = setInterval(() => {
                countdown--;
                if (countdown <= 0) {
                    window.location.href = '/register.html';
                    clearInterval(autoRedirect);
                }
            }, 1000);
            
            // Cancel auto-redirect if user interacts
            document.addEventListener('click', () => {
                clearInterval(autoRedirect);
            });
            
            document.addEventListener('keydown', () => {
                clearInterval(autoRedirect);
            });
        });
    </script>
</body>
</html>
