// Shein Fortune Wheel JavaScript
class FortuneWheel {
    constructor() {
        this.canvas = document.getElementById('wheelCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.centerX = this.canvas.width / 2;
        this.centerY = this.canvas.height / 2;
        this.radius = 280; // Increased from 180
        this.currentRotation = 0;
        this.isSpinning = false;
        this.currentClient = '';
        
        // Default prizes with Shein theme colors
        this.prizes = [
            { name: '10€ Gift Card', chance: 25, color: '#FF6BB7' },
            { name: '20€ Gift Card', chance: 20, color: '#4ECDC4' },
            { name: 'Free Shipping', chance: 25, color: '#FFB6C1' },
            { name: '15€ Gift Card', chance: 15, color: '#87CEEB' },
            { name: '25€ Gift Card', chance: 10, color: '#DDA0DD' },
            { name: '50€ Gift Card', chance: 5, color: '#98FB98' }
        ];
        
        this.winners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

        // Load saved prizes from localStorage
        const savedPrizes = localStorage.getItem('sheinPrizes');
        if (savedPrizes) {
            this.prizes = JSON.parse(savedPrizes);
        }

        this.init();
    }
    
    init() {
        this.drawWheel();
        this.setupEventListeners();
        this.updatePrizesList();
        this.updateWinnersHistory();
        this.updateDurationDisplay();
    }
    
    setupEventListeners() {
        document.getElementById('spinBtn').addEventListener('click', () => this.spin());
        document.getElementById('addPrize').addEventListener('click', () => this.addPrize());
        document.getElementById('resetWheel').addEventListener('click', () => this.resetWheel());
        document.getElementById('registerClient').addEventListener('click', () => this.registerClient());
        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        document.getElementById('clearHistory').addEventListener('click', () => this.clearHistory());
        document.getElementById('spinDuration').addEventListener('input', () => this.updateDurationDisplay());
        document.getElementById('closeModal').addEventListener('click', () => this.closeWinnerModal());

        // Close modal when clicking outside
        document.getElementById('winnerModal').addEventListener('click', (e) => {
            if (e.target.id === 'winnerModal') {
                this.closeWinnerModal();
            }
        });
    }
    
    drawWheel() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.prizes.length === 0) {
            this.ctx.fillStyle = '#f0f0f0';
            this.ctx.beginPath();
            this.ctx.arc(this.centerX, this.centerY, this.radius, 0, 2 * Math.PI);
            this.ctx.fill();
            
            this.ctx.fillStyle = '#666';
            this.ctx.font = '20px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('Add prizes to start', this.centerX, this.centerY);
            return;
        }
        
        const totalChance = this.prizes.reduce((sum, prize) => sum + prize.chance, 0);
        let currentAngle = 0;
        
        this.prizes.forEach((prize, index) => {
            const sliceAngle = (prize.chance / totalChance) * 2 * Math.PI;

            // Draw slice
            this.ctx.beginPath();
            this.ctx.moveTo(this.centerX, this.centerY);
            this.ctx.arc(this.centerX, this.centerY, this.radius, currentAngle, currentAngle + sliceAngle);
            this.ctx.closePath();
            this.ctx.fillStyle = prize.color;
            this.ctx.fill();

            // Draw border
            this.ctx.strokeStyle = '#fff';
            this.ctx.lineWidth = 3;
            this.ctx.stroke();

            // Draw text with better readability
            const textAngle = currentAngle + sliceAngle / 2;
            const textRadius = this.radius * 0.75;
            const textX = this.centerX + Math.cos(textAngle) * textRadius;
            const textY = this.centerY + Math.sin(textAngle) * textRadius;

            this.ctx.save();
            this.ctx.translate(textX, textY);
            this.ctx.rotate(textAngle + Math.PI / 2);

            // Determine font size based on text length and slice size
            const maxTextLength = 15;
            let fontSize = Math.min(18, Math.max(12, (sliceAngle * 180 / Math.PI) * 2));
            if (prize.name.length > maxTextLength) {
                fontSize = Math.max(10, fontSize * 0.8);
            }

            this.ctx.font = `bold ${fontSize}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // Add text shadow for better contrast
            this.ctx.strokeStyle = '#000';
            this.ctx.lineWidth = 3;
            this.ctx.strokeText(prize.name, 0, 0);

            this.ctx.fillStyle = '#fff';
            this.ctx.fillText(prize.name, 0, 0);
            this.ctx.restore();

            currentAngle += sliceAngle;
        });
    }
    
    spin() {
        if (this.isSpinning || this.prizes.length === 0) return;

        if (!this.currentClient) {
            alert('Please register a client first!');
            return;
        }

        this.isSpinning = true;
        document.getElementById('spinBtn').disabled = true;

        const duration = parseFloat(document.getElementById('spinDuration').value) * 1000;
        const minRotations = 15; // Increased from 5
        const maxRotations = 25; // Increased from 8
        const rotations = minRotations + Math.random() * (maxRotations - minRotations);
        const finalRotation = rotations * 360;

        // Determine winner based on weighted chances
        const winner = this.selectWinner();
        const winnerIndex = this.prizes.indexOf(winner);

        // Calculate the target angle more precisely
        const totalChance = this.prizes.reduce((sum, prize) => sum + prize.chance, 0);
        let targetAngle = 0;

        // Calculate cumulative angles for each prize
        for (let i = 0; i < winnerIndex; i++) {
            targetAngle += (this.prizes[i].chance / totalChance) * 360;
        }

        // Add half of the winner's slice to point to the center
        const winnerSliceAngle = (this.prizes[winnerIndex].chance / totalChance) * 360;
        targetAngle += winnerSliceAngle / 2;

        // The pointer is at the top (0 degrees), so we need to adjust
        // We want the target angle to be at the top when the wheel stops
        const finalAngle = finalRotation + (360 - targetAngle);

        this.animateWheel(finalAngle, duration, winner);
    }
    
    selectWinner() {
        const totalChance = this.prizes.reduce((sum, prize) => sum + prize.chance, 0);
        const random = Math.random() * totalChance;
        
        let currentChance = 0;
        for (const prize of this.prizes) {
            currentChance += prize.chance;
            if (random <= currentChance) {
                return prize;
            }
        }
        return this.prizes[0];
    }
    
    animateWheel(finalAngle, duration, winner) {
        const startTime = Date.now();
        const startRotation = this.currentRotation;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Enhanced easing function for more realistic deceleration
            // Starts very fast and slows down dramatically
            const easeOut = 1 - Math.pow(1 - progress, 4);

            this.currentRotation = startRotation + finalAngle * easeOut;

            this.ctx.save();
            this.ctx.translate(this.centerX, this.centerY);
            this.ctx.rotate((this.currentRotation * Math.PI) / 180);
            this.ctx.translate(-this.centerX, -this.centerY);
            this.drawWheel();
            this.ctx.restore();

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.onSpinComplete(winner);
            }
        };

        animate();
    }
    
    onSpinComplete(winner) {
        this.isSpinning = false;
        document.getElementById('spinBtn').disabled = false;

        // Add celebration effect
        this.showCelebration();

        // Display result with animation
        const resultDisplay = document.getElementById('resultDisplay');
        resultDisplay.style.background = 'linear-gradient(45deg, #FF6BB7, #4ECDC4)';
        resultDisplay.style.color = 'white';
        resultDisplay.style.transform = 'scale(1.05)';
        resultDisplay.innerHTML =
            `🎉 ${this.currentClient} won: <strong>${winner.name}</strong>! 🎉`;

        // Show winner popup modal
        this.showWinnerModal(this.currentClient, winner.name);

        // Reset result display style after 3 seconds
        setTimeout(() => {
            resultDisplay.style.background = '#f8f9fa';
            resultDisplay.style.color = '#333';
            resultDisplay.style.transform = 'scale(1)';
        }, 5000);

        // Save to history
        const winRecord = {
            client: this.currentClient,
            prize: winner.name,
            timestamp: new Date().toLocaleString()
        };

        this.winners.push(winRecord);
        localStorage.setItem('sheinWinners', JSON.stringify(this.winners));
        this.updateWinnersHistory();

        // Clear current client
        this.currentClient = '';
        document.getElementById('clientName').value = '';
    }

    showWinnerModal(clientName, prizeName) {
        document.getElementById('modalWinnerName').textContent = clientName;
        document.getElementById('modalWinnerPrize').textContent = prizeName;
        document.getElementById('winnerModal').style.display = 'block';

        // Prevent body scrolling when modal is open
        document.body.style.overflow = 'hidden';
    }

    closeWinnerModal() {
        document.getElementById('winnerModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    showCelebration() {
        // Simple celebration effect - could be enhanced with confetti library
        const wheel = document.getElementById('wheelCanvas');
        wheel.style.boxShadow = '0 0 30px #FF6BB7, 0 0 60px #4ECDC4';

        setTimeout(() => {
            wheel.style.boxShadow = '0 8px 25px rgba(0,0,0,0.3)';
        }, 2000);
    }
    
    addPrize() {
        const name = document.getElementById('prizeName').value.trim();
        const chance = parseInt(document.getElementById('prizeChance').value);
        const color = document.getElementById('prizeColor').value;

        if (!name || !chance || chance < 1 || chance > 100) {
            alert('Please enter valid prize details');
            return;
        }

        this.prizes.push({ name, chance, color });
        this.savePrizes(); // Save to localStorage
        this.drawWheel();
        this.updatePrizesList();

        // Clear form
        document.getElementById('prizeName').value = '';
        document.getElementById('prizeChance').value = '10';
        document.getElementById('prizeColor').value = '#FF6BB7';
    }
    
    removePrize(index) {
        this.prizes.splice(index, 1);
        this.savePrizes(); // Save to localStorage
        this.drawWheel();
        this.updatePrizesList();
    }
    
    updatePrizesList() {
        const container = document.getElementById('prizesList');
        container.innerHTML = '';

        this.prizes.forEach((prize, index) => {
            const prizeElement = document.createElement('div');
            prizeElement.className = 'prize-item';
            prizeElement.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <div class="prize-color" style="background-color: ${prize.color}"></div>
                    <div class="prize-info">
                        <strong>${prize.name}</strong> (${prize.chance}%)
                    </div>
                </div>
                <div class="prize-actions">
                    <button class="btn-secondary" onclick="wheel.editPrize(${index})" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;">Edit</button>
                    <button class="btn-danger" onclick="wheel.removePrize(${index})" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;">Remove</button>
                </div>
            `;
            container.appendChild(prizeElement);
        });
    }

    editPrize(index) {
        const prize = this.prizes[index];
        const newName = prompt('Enter new prize name:', prize.name);
        if (newName === null) return;

        const newChance = prompt('Enter new winning chance (1-100):', prize.chance);
        if (newChance === null) return;

        const chance = parseInt(newChance);
        if (isNaN(chance) || chance < 1 || chance > 100) {
            alert('Please enter a valid chance between 1 and 100');
            return;
        }

        const newColor = prompt('Enter new color (hex code):', prize.color);
        if (newColor === null) return;

        this.prizes[index] = {
            name: newName.trim() || prize.name,
            chance: chance,
            color: newColor.trim() || prize.color
        };

        this.savePrizes(); // Save to localStorage
        this.drawWheel();
        this.updatePrizesList();
    }
    
    registerClient() {
        const name = document.getElementById('clientName').value.trim();
        if (!name) {
            alert('Please enter a client name');
            return;
        }
        
        this.currentClient = name;
        document.getElementById('resultDisplay').innerHTML = 
            `Client registered: <strong>${name}</strong>. Ready to spin!`;
    }
    
    resetWheel() {
        this.currentRotation = 0;
        this.drawWheel();
        document.getElementById('resultDisplay').innerHTML = '';
    }
    
    updateWinnersHistory() {
        const container = document.getElementById('historyList');
        container.innerHTML = '';
        
        this.winners.slice(-10).reverse().forEach(winner => {
            const historyElement = document.createElement('div');
            historyElement.className = 'history-item';
            historyElement.innerHTML = `
                <div><strong>${winner.client}</strong> won <em>${winner.prize}</em></div>
                <div class="timestamp">${winner.timestamp}</div>
            `;
            container.appendChild(historyElement);
        });
    }
    
    exportData() {
        if (this.winners.length === 0) {
            alert('No data to export');
            return;
        }

        try {
            // Create workbook
            const wb = XLSX.utils.book_new();

            // Create winners data
            const winnersData = this.winners.map(winner => ({
                'Client Name': winner.client || '',
                'Prize': winner.prize || '',
                'Date': winner.timestamp || ''
            }));

            const winnersWS = XLSX.utils.json_to_sheet(winnersData);
            XLSX.utils.book_append_sheet(wb, winnersWS, 'Winners');

            // Generate and download file
            const fileName = `shein-winners-${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);

            alert('Data exported successfully!');
        } catch (error) {
            console.error('Error exporting data:', error);
            // Fallback to text export
            const data = this.winners.map(w =>
                `${w.timestamp} - ${w.client}: ${w.prize}`
            ).join('\n');

            const blob = new Blob([data], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'shein_winners.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
    }
    
    clearHistory() {
        if (confirm('Are you sure you want to clear all winner history?')) {
            this.winners = [];
            localStorage.removeItem('sheinWinners');
            this.updateWinnersHistory();
        }
    }
    
    updateDurationDisplay() {
        const duration = document.getElementById('spinDuration').value;
        document.getElementById('durationValue').textContent = duration;
    }

    savePrizes() {
        localStorage.setItem('sheinPrizes', JSON.stringify(this.prizes));
    }
}

// Collapsible section functionality
function toggleSection(sectionId) {
    const content = document.getElementById(sectionId + 'Content');
    const icon = document.getElementById(sectionId + 'Icon');

    if (content.classList.contains('collapsed')) {
        content.classList.remove('collapsed');
        icon.classList.remove('rotated');
        icon.textContent = '▼';
    } else {
        content.classList.add('collapsed');
        icon.classList.add('rotated');
        icon.textContent = '▶';
    }
}

// Initialize the wheel when page loads
let wheel;
document.addEventListener('DOMContentLoaded', () => {
    wheel = new FortuneWheel();
});
