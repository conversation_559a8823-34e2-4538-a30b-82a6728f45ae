# Shein Fortune Wheel

A beautiful, interactive fortune wheel application with Shein branding colors and comprehensive prize management features.

## Features

### 🎯 Core Functionality
- **Interactive Fortune Wheel**: Smooth spinning animation with realistic deceleration
- **Customizable Prizes**: Add, edit, and remove prizes with custom names, colors, and winning chances
- **Client Management**: Register clients before spinning and track their wins
- **Winner History**: Automatic tracking of all winners with timestamps
- **Data Export**: Export winner history to text file

### 🎨 Visual Design
- **Shein Color Scheme**: Pink (#FF6BB7) and teal (#4ECDC4) gradient theme matching the provided image
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Realistic spinning with easing and celebration effects
- **Center Logo**: Shein-branded center piece with pointer indicator

### ⚙️ Customization Options
- **Adjustable Spin Duration**: Control how long the wheel spins (2-8 seconds)
- **Weighted Chances**: Set different winning probabilities for each prize
- **Color Customization**: Choose custom colors for each prize segment
- **Prize Management**: Real-time editing of prize details

## How to Use

### 1. Setup
- Open `index.html` in any modern web browser
- No installation or server required - runs entirely in the browser

### 2. Managing Prizes
1. **Add New Prize**:
   - Enter prize name (e.g., "20% Discount")
   - Set winning chance percentage (1-100%)
   - Choose a color for the wheel segment
   - Click "Add Prize"

2. **Edit Existing Prize**:
   - Click "Edit" button next to any prize
   - Modify name, chance, or color as needed

3. **Remove Prize**:
   - Click "Remove" button next to unwanted prizes

### 3. Spinning the Wheel
1. **Register Client**:
   - Enter client name in the "Client Information" section
   - Click "Register Client"

2. **Spin**:
   - Click "SPIN THE WHEEL" button
   - Watch the smooth animation
   - See the winning result with celebration effects

### 4. Managing Data
- **View History**: See the last 10 winners in the "Winners History" section
- **Export Data**: Click "Export to Text File" to download complete winner history
- **Clear History**: Remove all historical data (with confirmation)

### 5. Wheel Settings
- **Spin Duration**: Adjust how long the wheel spins using the slider
- **Reset Wheel**: Return wheel to starting position

## Technical Details

### Files Structure
```
shein-wheel/
├── index.html          # Main HTML structure
├── styles.css          # Shein-themed styling
├── script.js           # Interactive functionality
└── README.md           # This documentation
```

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Data Storage
- Winner history is stored in browser's localStorage
- Data persists between sessions
- Export feature creates downloadable text files

### Default Prizes
The wheel comes pre-loaded with Shein-themed prizes:
- 10% Discount (25% chance)
- 20% Discount (20% chance)
- Free Shipping (25% chance)
- 30% Discount (15% chance)
- Gift Card $10 (10% chance)
- Gift Card $25 (5% chance)

## Customization Tips

### Color Scheme
The application uses Shein's signature colors:
- Primary Pink: `#FF6BB7`
- Secondary Teal: `#4ECDC4`
- Light Pink: `#FFB6C1`
- Light Blue: `#87CEEB`

### Winning Chances
- Total percentages don't need to equal 100%
- Higher percentages = more likely to win
- Minimum chance: 1%, Maximum: 100%

### Prize Ideas
- Discount percentages (10%, 20%, 30%, etc.)
- Free shipping offers
- Gift cards with different values
- Buy-one-get-one deals
- Exclusive access to sales
- Free items or samples

## Troubleshooting

### Common Issues
1. **Wheel won't spin**: Make sure a client is registered first
2. **No prizes showing**: Add at least one prize to see the wheel
3. **Data not saving**: Check if browser allows localStorage
4. **Export not working**: Ensure browser supports file downloads

### Browser Requirements
- JavaScript must be enabled
- localStorage support required for data persistence
- Canvas support needed for wheel rendering

## Future Enhancements
- Sound effects for spinning and winning
- Confetti animation for celebrations
- Multiple wheel templates
- Advanced statistics and analytics
- Social media sharing integration
- Multi-language support

---

**Created for Shein promotional events and customer engagement activities.**
