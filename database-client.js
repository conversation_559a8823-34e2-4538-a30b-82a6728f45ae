// Database Client for Shein Fortune Wheel
// Simple wrapper that uses localStorage for immediate functionality

class DatabaseClient {
    constructor() {
        // JSONBin.io cloud database configuration
        this.useCloudDB = true;
        this.jsonBinUrl = 'https://api.jsonbin.io/v3/b';
        this.binId = '68ab525dd0ea881f4063078c';
        this.apiKey = '$2a$10$Twch63QhrK5EKGmGCrECfOxy0whAiFxFGWcDyOdWoKpuO0cpaIBge';
        this.fallbackToLocal = true;
        this.connectionTested = true;
        this.storageKey = 'sheinFortuneWheelDB';
    }

    // Generic API call method
    async apiCall(action, data = {}) {
        if (this.useCloudDB) {
            try {
                return await this.handleCloudDatabase(action, data);
            } catch (error) {
                console.error(`Cloud database failed for action ${action}:`, error);
                if (this.fallbackToLocal) {
                    console.log('Falling back to localStorage');
                    return this.handleLocalFallback(action, data);
                }
                throw error;
            }
        } else {
            return this.handleLocalFallback(action, data);
        }
    }

    // Cloud database operations using JSONBin.io
    async handleCloudDatabase(action, data) {
        try {
            // First, get current database state
            const currentData = await this.getCloudData();

            // Perform the action on the data
            const result = await this.performCloudAction(action, data, currentData);

            // Save back to cloud if data was modified
            if (result.modified) {
                await this.saveCloudData(result.data);
            }

            return result.response;
        } catch (error) {
            console.error('Cloud database operation failed:', error);
            throw error;
        }
    }

    // Get data from JSONBin.io
    async getCloudData() {
        try {
            const response = await fetch(`${this.jsonBinUrl}/${this.binId}/latest`, {
                method: 'GET',
                headers: {
                    'X-Master-Key': this.apiKey,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                if (response.status === 404) {
                    // Bin doesn't exist, return initial data
                    return {
                        clients: [],
                        winners: [],
                        settings: {
                            created: new Date().toISOString(),
                            version: '1.0'
                        }
                    };
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return result.record;
        } catch (error) {
            console.error('Error getting cloud data:', error);
            throw error;
        }
    }

    // Save data to JSONBin.io
    async saveCloudData(data) {
        try {
            data.settings = {
                ...data.settings,
                lastUpdated: new Date().toISOString()
            };

            const response = await fetch(`${this.jsonBinUrl}/${this.binId}`, {
                method: 'PUT',
                headers: {
                    'X-Master-Key': this.apiKey,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return true;
        } catch (error) {
            console.error('Error saving cloud data:', error);
            throw error;
        }
    }
    // Perform actions on cloud data
    async performCloudAction(action, data, currentData) {
        const clients = currentData.clients || [];
        const winners = currentData.winners || [];
        let modified = false;

        switch (action) {
            case 'getClient':
                const client = clients.find(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );
                return {
                    modified: false,
                    response: { success: true, client: client || null, found: !!client }
                };

            case 'addClient':
                const existingClient = clients.find(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );
                if (existingClient) {
                    return {
                        modified: false,
                        response: { success: false, error: 'Client already exists' }
                    };
                }

                const newClient = {
                    clientName: data.clientName,
                    paymentAmount: data.paymentAmount,
                    attempts: data.attempts || 0,
                    hasWon: data.hasWon || false,
                    lastPrize: '',
                    lastWinDate: '',
                    totalWinnings: 0,
                    createdAt: new Date().toISOString()
                };

                clients.push(newClient);
                return {
                    modified: true,
                    data: { ...currentData, clients },
                    response: { success: true, client: newClient }
                };

            case 'updateClient':
                const clientIndex = clients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );

                if (clientIndex === -1) {
                    return {
                        modified: false,
                        response: { success: false, error: 'Client not found' }
                    };
                }

                clients[clientIndex] = {
                    ...clients[clientIndex],
                    ...data.updates,
                    updatedAt: new Date().toISOString()
                };

                return {
                    modified: true,
                    data: { ...currentData, clients },
                    response: { success: true, client: clients[clientIndex] }
                };

            case 'getAllClients':
                return {
                    modified: false,
                    response: { success: true, clients, count: clients.length }
                };

            case 'deleteClient':
                const deleteIndex = clients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );

                if (deleteIndex === -1) {
                    return {
                        modified: false,
                        response: { success: false, error: 'Client not found' }
                    };
                }

                clients.splice(deleteIndex, 1);
                return {
                    modified: true,
                    data: { ...currentData, clients },
                    response: { success: true, message: 'Client deleted successfully' }
                };

            case 'addWinner':
                const winner = {
                    clientName: data.clientName,
                    prize: data.prize,
                    timestamp: data.timestamp || new Date().toISOString(),
                    id: Date.now().toString()
                };

                winners.push(winner);
                return {
                    modified: true,
                    data: { ...currentData, winners },
                    response: { success: true, winner }
                };

            case 'getWinners':
                return {
                    modified: false,
                    response: { success: true, winners, count: winners.length }
                };

            case 'importClients':
                let imported = 0;
                let updated = 0;

                for (const clientData of data.clients) {
                    const existingIndex = clients.findIndex(c =>
                        c.clientName && c.clientName.toLowerCase() === clientData.clientName.toLowerCase()
                    );

                    if (existingIndex >= 0) {
                        clients[existingIndex] = {
                            ...clients[existingIndex],
                            ...clientData,
                            updatedAt: new Date().toISOString()
                        };
                        updated++;
                    } else {
                        clients.push({
                            ...clientData,
                            attempts: clientData.attempts || 0,
                            hasWon: clientData.hasWon || false,
                            lastPrize: clientData.lastPrize || '',
                            lastWinDate: clientData.lastWinDate || '',
                            totalWinnings: clientData.totalWinnings || 0,
                            createdAt: new Date().toISOString()
                        });
                        imported++;
                    }
                }

                return {
                    modified: true,
                    data: { ...currentData, clients },
                    response: { success: true, imported, updated, total: data.clients.length }
                };

            default:
                return {
                    modified: false,
                    response: { success: false, error: 'Invalid action' }
                };
        }
    }




    // Fallback to localStorage when database is unavailable
    handleLocalFallback(action, data) {
        const localClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
        const localWinners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

        switch (action) {
            case 'getClient':
                const client = localClients.find(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );
                return { success: true, client: client || null, found: !!client };

            case 'getAllClients':
                return { success: true, clients: localClients, count: localClients.length };

            case 'addClient':
                const existingIndex = localClients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );
                if (existingIndex >= 0) {
                    return { success: false, error: 'Client already exists' };
                }

                const newClient = {
                    clientName: data.clientName,
                    paymentAmount: data.paymentAmount,
                    attempts: data.attempts || 0,
                    hasWon: data.hasWon || false,
                    lastPrize: '',
                    lastWinDate: '',
                    totalWinnings: 0,
                    createdAt: new Date().toISOString()
                };

                localClients.push(newClient);
                localStorage.setItem('sheinClients', JSON.stringify(localClients));
                return { success: true, client: newClient };

            case 'updateClient':
                const updateIndex = localClients.findIndex(c =>
                    c.clientName && c.clientName.toLowerCase() === data.clientName.toLowerCase()
                );
                if (updateIndex === -1) {
                    return { success: false, error: 'Client not found' };
                }

                localClients[updateIndex] = {
                    ...localClients[updateIndex],
                    ...data.updates,
                    updatedAt: new Date().toISOString()
                };

                localStorage.setItem('sheinClients', JSON.stringify(localClients));
                return { success: true, client: localClients[updateIndex] };

            case 'addWinner':
                const winner = {
                    clientName: data.clientName,
                    prize: data.prize,
                    timestamp: data.timestamp || new Date().toISOString(),
                    id: Date.now().toString()
                };

                localWinners.push(winner);
                localStorage.setItem('sheinWinners', JSON.stringify(localWinners));
                return { success: true, winner };

            case 'getWinners':
                return { success: true, winners: localWinners, count: localWinners.length };

            default:
                return { success: false, error: 'Action not supported in fallback mode' };
        }
    }

    // Get client by name
    async getClient(clientName) {
        return await this.apiCall('getClient', { clientName });
    }

    // Add new client
    async addClient(clientName, paymentAmount, attempts = 0, hasWon = false) {
        return await this.apiCall('addClient', {
            clientName,
            paymentAmount,
            attempts,
            hasWon
        });
    }

    // Update client data
    async updateClient(clientName, updates) {
        return await this.apiCall('updateClient', {
            clientName,
            updates
        });
    }

    // Get all clients
    async getAllClients() {
        return await this.apiCall('getAllClients');
    }

    // Delete client
    async deleteClient(clientName) {
        return await this.apiCall('deleteClient', { clientName });
    }

    // Add winner record
    async addWinner(clientName, prize, timestamp = null) {
        return await this.apiCall('addWinner', {
            clientName,
            prize,
            timestamp
        });
    }

    // Get all winners
    async getWinners() {
        return await this.apiCall('getWinners');
    }

    // Import multiple clients
    async importClients(clients) {
        return await this.apiCall('importClients', { clients });
    }

    // Reset database (admin only)
    async resetDatabase() {
        return await this.apiCall('resetDatabase');
    }

    // Sync local data to database (for migration)
    async syncLocalToDatabase() {
        try {
            const localClients = JSON.parse(localStorage.getItem('sheinClients') || '[]');
            const localWinners = JSON.parse(localStorage.getItem('sheinWinners') || '[]');

            if (localClients.length > 0) {
                console.log('Syncing local clients to database...');
                const result = await this.importClients(localClients);
                console.log('Sync result:', result);
            }

            if (localWinners.length > 0) {
                console.log('Syncing local winners to database...');
                for (const winner of localWinners) {
                    await this.addWinner(winner.clientName, winner.prize, winner.timestamp);
                }
            }

            return { success: true, message: 'Local data synced to database' };
        } catch (error) {
            console.error('Error syncing local data:', error);
            return { success: false, error: error.message };
        }
    }

    // Check database connectivity
    async checkConnection() {
        if (!this.useCloudDB) {
            return { connected: false, fallback: true };
        }

        try {
            // Test connection by trying to get data
            await this.getCloudData();
            this.connectionTested = true;
            return { connected: true, fallback: false };
        } catch (error) {
            console.warn('Cloud database connection failed, using localStorage fallback');
            this.connectionTested = true;
            this.useCloudDB = false; // Disable cloud DB for this session
            return { connected: false, fallback: true };
        }
    }

    // Show connection status to user
    showConnectionStatus() {
        // Create status element if it doesn't exist
        let statusElement = document.getElementById('connectionStatus');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'connectionStatus';
            statusElement.className = 'connection-status checking';

            // Add to client actions area
            const clientActions = document.querySelector('.client-actions');
            if (clientActions) {
                clientActions.appendChild(statusElement);
            }
        }

        // Show immediate status
        statusElement.innerHTML = '🔄 Checking connection...';
        statusElement.className = 'connection-status checking';

        this.checkConnection().then(status => {
            if (status.connected) {
                statusElement.innerHTML = '🟢 Cloud database connected';
                statusElement.className = 'connection-status connected';
                console.log('✅ Connected to cloud database - data syncs across all devices');
            } else {
                statusElement.innerHTML = '🟡 Local storage mode';
                statusElement.className = 'connection-status offline';
                console.log('⚠️ Using local storage - data is device-specific');
            }
        }).catch(error => {
            statusElement.innerHTML = '🟡 Local storage mode';
            statusElement.className = 'connection-status offline';
            console.log('❌ Cloud database failed, using local storage');
        });
    }
}

// Create global database client instance
window.dbClient = new DatabaseClient();
